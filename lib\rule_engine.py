"""
Rule Engine for AutoPlace Tab Plugin

Centralized rule matching and target group determination logic.
Single source of truth for all placement decisions.
"""

from __future__ import annotations
import os
import fnmatch
from typing import Optional, Dict, List, Any, Set
import sublime


class RuleEngine:
    """Centralized rule engine for tab placement decisions."""
    
    def __init__(self, plugin):
        """Initialize rule engine with plugin reference."""
        self.plugin = plugin
    
    def determine_target_group(self, view: sublime.View) -> Optional[int]:
        """
        Determine the target group for a view based on layout-centric rules.
        
        Args:
            view: The Sublime Text view to place
            
        Returns:
            Target group index or None if no placement should occur
        """
        window = view.window()
        if not window:
            return None

        # Use layout-centric approach only
        layout_def = self.plugin.layout_manager.get_active_layout(window)
        if layout_def:
            return self._determine_target_group_layout_centric(view, window, layout_def)

        self.plugin._debug_print("No active layout configured")
        return None
    
    def _determine_target_group_layout_centric(self, view: sublime.View, window: sublime.Window, layout_def) -> Optional[int]:
        """
        Determine target group using layout-centric approach.
        
        Args:
            view: The view to place
            window: The window containing the view
            layout_def: The active layout definition
            
        Returns:
            Target group index or None
        """
        # Check exclude patterns first
        if self.should_exclude_file(view, window):
            self.plugin._debug_print("File excluded by exclude_patterns")
            return None

        # Get semantic types
        semantic_types = self.plugin.semantic_detector.get_semantic_types(view, window, layout_def)
        self.plugin._debug_print(f"Semantic types for {view.file_name()}: {semantic_types}")

        # Check each group's rules in order
        for group_str, rules in layout_def.group_rules.items():
            group_id = int(group_str)
            
            for rule_def in rules:
                if self.matches_rule(view, rule_def, semantic_types):
                    self.plugin._debug_print(f"Matched rule in group {group_id}: {rule_def.get('description', 'Unnamed')}")
                    return group_id

        self.plugin._debug_print("No matching rules found")
        return None
    
    def matches_rule(self, view: sublime.View, rule_def: Dict[str, Any], semantic_types: Set[str]) -> bool:
        """
        Check if a view matches a layout rule definition.
        
        Args:
            view: The view to check
            rule_def: The rule definition to match against
            semantic_types: Set of semantic types for the view
            
        Returns:
            True if the view matches the rule
        """
        match_conditions = rule_def.get("match", {})
        exclude_conditions = rule_def.get("exclude", {})

        # Check match conditions
        if not self.matches_conditions(view, match_conditions, semantic_types):
            return False

        # Check exclude conditions
        if exclude_conditions and self.matches_conditions(view, exclude_conditions, semantic_types):
            return False

        return True
    
    def should_exclude_file(self, view: sublime.View, window: sublime.Window) -> bool:
        """
        Check if file should be excluded from auto-placement.
        
        Args:
            view: The view to check
            window: The window containing the view
            
        Returns:
            True if the file should be excluded
        """
        file_path = view.file_name()
        if not file_path:
            return False

        exclude_patterns = self.plugin._get_setting("exclude_patterns", [], window)
        normalized_path = self.plugin._normalize_path(file_path)
        file_name = os.path.basename(normalized_path)

        for pattern in exclude_patterns:
            # Check both full path and filename against pattern
            if fnmatch.fnmatch(normalized_path, pattern) or fnmatch.fnmatch(file_name, pattern):
                return True
        return False
    
    def matches_conditions(self, view: sublime.View, conditions: Dict[str, Any], file_types: Set[str]) -> bool:
        """
        Check if a view matches a set of conditions.
        
        Args:
            view: The view to check
            conditions: Dictionary of conditions to match
            file_types: Set of semantic types for the view
            
        Returns:
            True if all conditions are met
        """
        # Check semantic types
        required_types = conditions.get("types", [])
        if required_types and not all(t in file_types for t in required_types):
            return False

        file_path = view.file_name()
        if not file_path:
            # If no file path, can only match on semantic types
            return True

        # Normalize path once for consistent matching
        normalized_path = self.plugin._normalize_path(file_path)
        file_name = os.path.basename(normalized_path)

        # Check extensions
        extensions = conditions.get("extensions", [])
        if extensions:
            file_ext = os.path.splitext(normalized_path)[1].lower()
            if file_ext not in [ext.lower() for ext in extensions]:
                return False

        # Check file name patterns
        file_name_patterns = conditions.get("file_name_patterns", [])
        if file_name_patterns:
            if not any(fnmatch.fnmatch(file_name, pattern) for pattern in file_name_patterns):
                return False

        # Check directory patterns
        directory_patterns = conditions.get("directory_patterns", [])
        if directory_patterns:
            if not any(fnmatch.fnmatch(normalized_path, pattern) for pattern in directory_patterns):
                return False

        return True
