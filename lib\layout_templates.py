"""
Layout Template Generator for Jorn_AutoPlaceTabs

Provides predefined layout templates and generation utilities.
"""

import sublime


class LayoutTemplates:
    """Predefined layout templates for common workflows."""
    
    @staticmethod
    def get_web_development_template():
        """Get web development layout template."""
        return {
            "name": "Web Development Layout",
            "description": "Optimized for frontend development workflow",
            "layout_type": "custom",
            "layout_definition": {
                "cols": [0.0, 0.4, 0.7, 1.0],
                "rows": [0.0, 0.6, 1.0],
                "cells": [
                    [0, 0, 1, 2],  # Group 0: Main code (tall)
                    [1, 0, 2, 1],  # Group 1: Tests
                    [2, 0, 3, 1],  # Group 2: Styles
                    [1, 1, 2, 2],  # Group 3: Temp/scratch
                    [2, 1, 3, 2]   # Group 4: External/deleted
                ]
            },
            "group_rules": {
                "0": [{
                    "description": "Main source files being actively worked on",
                    "match": {
                        "extensions": [".js", ".ts", ".jsx", ".tsx", ".vue", ".svelte"],
                        "types": ["project", "exists"],
                        "directory_patterns": ["*/src/*", "*/components/*", "*/pages/*"]
                    },
                    "exclude": {
                        "types": ["deleted", "empty"],
                        "file_name_patterns": ["*.test.*", "*.spec.*"]
                    }
                }],
                "1": [{
                    "description": "All test files",
                    "match": {
                        "file_name_patterns": ["*.test.*", "*.spec.*", "*_test.*"],
                        "directory_patterns": ["*/tests/*", "*/__tests__/*", "*/test/*"],
                        "types": ["exists"]
                    }
                }],
                "2": [{
                    "description": "CSS, images, and styling files",
                    "match": {
                        "extensions": [".css", ".scss", ".sass", ".less", ".styl"],
                        "types": ["project", "exists"]
                    }
                }],
                "3": [{
                    "description": "Unsaved work and temporary files",
                    "match": {
                        "types": ["unsaved", "scratch", "empty", "short_lived"]
                    }
                }],
                "4": [{
                    "description": "External files, deleted files, or large files",
                    "match": {
                        "types": ["external", "deleted", "large_file", "stale"]
                    }
                }]
            },
            "settings": {
                "recently_opened_threshold": 300,
                "short_lived_threshold": 10,
                "large_file_threshold": 1048576,
                "group_sort_method": "alphabetical"
            }
        }
    
    @staticmethod
    def get_python_data_science_template():
        """Get Python data science layout template."""
        return {
            "name": "Python Data Science",
            "description": "Layout for ML and data analysis projects",
            "layout_type": "custom",
            "layout_definition": {
                "cols": [0.0, 0.6, 1.0],
                "rows": [0.0, 0.5, 1.0],
                "cells": [
                    [0, 0, 1, 1],  # Group 0: Source (large)
                    [1, 0, 2, 1],  # Group 1: Notebooks
                    [0, 1, 1, 2],  # Group 2: Tests
                    [1, 1, 2, 2]   # Group 3: Data
                ]
            },
            "group_rules": {
                "0": [{
                    "description": "Python source files",
                    "match": {
                        "extensions": [".py"],
                        "directory_patterns": ["*/src/*", "*/lib/*", "*/models/*"],
                        "types": ["project", "exists"]
                    },
                    "exclude": {
                        "file_name_patterns": ["test_*", "*_test.py"]
                    }
                }],
                "1": [{
                    "description": "Jupyter notebooks and analysis scripts",
                    "match": {
                        "extensions": [".ipynb", ".py"],
                        "directory_patterns": ["*/notebooks/*", "*/analysis/*", "*/scripts/*"]
                    }
                }],
                "2": [{
                    "description": "Test files",
                    "match": {
                        "file_name_patterns": ["test_*", "*_test.py", "conftest.py"],
                        "directory_patterns": ["*/tests/*"]
                    }
                }],
                "3": [{
                    "description": "Data files and configuration",
                    "match": {
                        "extensions": [".csv", ".json", ".parquet", ".yaml", ".yml", ".toml"],
                        "directory_patterns": ["*/data/*", "*/config/*"]
                    }
                }]
            },
            "settings": {
                "large_file_threshold": 10485760,  # 10MB for data files
                "group_sort_method": "alphabetical"
            }
        }
    
    @staticmethod
    def get_cleanup_mode_template():
        """Get cleanup mode layout template."""
        return {
            "name": "Cleanup Mode",
            "description": "Organize tabs for cleanup - separate problematic tabs",
            "layout_type": "custom",
            "layout_definition": {
                "cols": [0.0, 0.5, 1.0],
                "rows": [0.0, 0.33, 0.66, 1.0],
                "cells": [
                    [0, 0, 1, 1],  # Good files
                    [1, 0, 2, 1],  # Dirty files
                    [0, 1, 1, 2],  # Deleted/missing
                    [1, 1, 2, 2],  # External files
                    [0, 2, 2, 3]   # Empty/scratch (wide)
                ]
            },
            "group_rules": {
                "0": [{
                    "description": "Saved project files without issues",
                    "match": {
                        "types": ["project", "saved", "exists", "non_empty"]
                    },
                    "exclude": {
                        "types": ["stale", "large_file"]
                    }
                }],
                "1": [{
                    "description": "Files with unsaved changes",
                    "match": {
                        "types": ["dirty", "exists"]
                    }
                }],
                "2": [{
                    "description": "Files that no longer exist",
                    "match": {
                        "types": ["deleted", "missing"]
                    }
                }],
                "3": [{
                    "description": "Files outside project folders",
                    "match": {
                        "types": ["external", "exists"]
                    }
                }],
                "4": [{
                    "description": "Empty files and scratch buffers",
                    "match": {
                        "types": ["empty", "scratch", "unsaved"]
                    }
                }]
            }
        }
    
    @staticmethod
    def get_simple_two_column_template():
        """Get simple two-column layout template."""
        return {
            "name": "Simple Two Column",
            "description": "Basic two-column layout for any project",
            "layout_type": "columns",
            "layout_definition": {
                "cols": [0.0, 0.5, 1.0],
                "rows": [0.0, 1.0],
                "cells": [
                    [0, 0, 1, 1],  # Group 0: Main files
                    [1, 0, 2, 1]   # Group 1: Other files
                ]
            },
            "group_rules": {
                "0": [{
                    "description": "Files within the project",
                    "match": {
                        "types": ["project"]
                    }
                }],
                "1": [{
                    "description": "External and temporary files",
                    "match": {
                        "types": ["external", "unsaved", "scratch"]
                    }
                }]
            }
        }
    
    @staticmethod
    def get_all_templates():
        """Get all available layout templates."""
        return {
            "web_development": LayoutTemplates.get_web_development_template(),
            "python_data_science": LayoutTemplates.get_python_data_science_template(),
            "cleanup_mode": LayoutTemplates.get_cleanup_mode_template(),
            "simple_two_column": LayoutTemplates.get_simple_two_column_template()
        }
    
    @staticmethod
    def get_template_names():
        """Get list of available template names with descriptions."""
        templates = LayoutTemplates.get_all_templates()
        return [
            (name, template["name"], template["description"])
            for name, template in templates.items()
        ]
