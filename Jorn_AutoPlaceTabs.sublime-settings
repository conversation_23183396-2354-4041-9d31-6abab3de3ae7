{
    // ============================================================================
    // JORN AUTO PLACE TABS - LAYOUT-CENTRIC REFERENCE
    // ============================================================================
    // This file serves as a lookup reference for all available functionality.
    // Copy and customize layout structures in your .sublime-project files.
    // ============================================================================

    // GLOBAL SETTINGS
    // ============================================================================
    // Note: Plugin requires project-specific layout configuration to function
    // Use "Create Layout from Template" command to get started
    "auto_place_on_load": false,
    "auto_place_on_activation": false,
    "enable_debug_prints": false,
    "exclude_patterns": [],

    // Performance settings
    "max_placements_per_second": 20,
    "large_file_threshold": 1048576,  // 1MB - skip content analysis for larger files
    "project_file_check_interval": 2.0,  // Seconds between project file mtime checks
                                          // Increase to 5.0+ for network drives to reduce I/O

    // Example exclude patterns (cross-platform compatible)
    // "exclude_patterns": [
    //     "*.tmp",
    //     "*/.git/*",
    //     "*/node_modules/*",
    //     "*/build/*",
    //     "*/__pycache__/*"
    // ],

    // SEMANTIC TYPES REFERENCE
    // ============================================================================
    "defined_types": {
        // Basic states
        "unsaved": "Tab has never been saved to disk",
        "dirty": "Tab has unsaved changes",
        "saved": "Tab is saved (not dirty)",
        "scratch": "Scratch buffer (not file-backed)",
        "readonly": "Tab is read-only",

        // Location states
        "project": "File is inside a project folder",
        "external": "File is outside all project folders",

        // File existence states
        "deleted": "File no longer exists on disk",
        "missing": "File path exists but file is missing",
        "exists": "File exists on disk",

        // Content states
        "empty": "Tab has no content or only whitespace",
        "non_empty": "Tab has meaningful content",

        // Activity states
        "active": "Currently active/focused tab",
        "inactive": "Not the currently active tab",
        "visible": "Tab is visible in current group",
        "background": "Tab is in a background group",

        // Time-based states
        "recently_opened": "Opened within recent time threshold",
        "short_lived": "Open for less than short_lived_threshold",
        "inactive_file": "File not modified recently (based on file system)",
        "stale": "Tab hasn't been focused for a long time",

        // Size-based states
        "large_file": "File size exceeds large_file_threshold",
        "small_file": "File size below small_file_threshold",

        // Syntax states
        "has_syntax": "Tab has a specific syntax assigned",
        "plain_text": "Tab is plain text (no syntax highlighting)"
    },

    // PROJECT SETTINGS STRUCTURE REFERENCE
    // ============================================================================
    // Copy this structure to your .sublime-project file under:
    // "settings" -> "jorn_auto_place_tabs"
    "project_structure_example": {
        "auto_place_on_activation": true,
        "active_layout": "development",
        "layouts": {
            "development": {
                "name": "Development Layout",
                "description": "Main development workflow",
                "layout_type": "custom",
                "layout_definition": {
                    "cols": [0.0, 0.5, 1.0],
                    "rows": [0.0, 1.0],
                    "cells": [[0,0,1,1], [1,0,2,1]]
                },
                "group_rules": {
                    "0": [{
                        "description": "Source files",
                        "match": {
                            "extensions": [".py", ".js", ".ts"],
                            "directory_patterns": ["*/src/*", "*/lib/*"],
                            "types": ["project", "exists"]
                        },
                        "exclude": {
                            "types": ["deleted", "empty"]
                        }
                    }],
                    "1": [{
                        "description": "External and temporary files",
                        "match": {
                            "types": ["external", "unsaved", "scratch"]
                        }
                    }]
                },
                "settings": {
                    "recently_opened_threshold": 300,
                    "short_lived_threshold": 10,
                    "large_file_threshold": 1048576,
                    "small_file_threshold": 1024,
                    "inactive_file_threshold": 86400,
                    "stale_threshold": 3600
                }
            }
        }
    }
}
