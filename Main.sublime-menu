[{"caption": "Tools", "mnemonic": "T", "id": "tools", "children": [{"caption": "<PERSON><PERSON>", "id": "jorn_auto_place_tabs", "children": [{"caption": "Layout Management", "id": "layout_management", "children": [{"caption": "Switch Layout…", "command": "jorn_auto_place_tabs_switch_layout"}, {"caption": "Create from Template…", "command": "jorn_auto_place_tabs_create_layout_from_template"}, {"caption": "-"}, {"caption": "Project Information", "command": "jorn_auto_place_tabs_project_info"}, {"caption": "Export Layouts…", "command": "jorn_auto_place_tabs_export_layouts"}, {"caption": "Cleanup Settings", "command": "jorn_auto_place_tabs_cleanup_project_settings"}]}, {"caption": "-"}, {"caption": "Place Current Tab", "command": "jorn_auto_place_tabs_manual"}, {"caption": "Apply Active Layout", "command": "jorn_auto_place_tabs_place_all"}, {"caption": "-"}, {"caption": "Toggle Auto-Placement", "command": "jorn_auto_place_tabs_toggle"}, {"caption": "-"}, {"caption": "Debug & Information", "id": "debug_info", "children": [{"caption": "Show Semantic Types", "command": "jorn_auto_place_tabs_debug_semantic_types"}, {"caption": "Show Current Rules", "command": "jorn_auto_place_tabs_show_rules"}, {"caption": "Reload Settings", "command": "jorn_auto_place_tabs_reload_settings"}]}]}]}]