# Jorn_AutoPlaceTabs

A Sublime Text 4 plugin that automatically organizes tabs into groups using intelligent layout-centric rules based on file type, directory patterns, and semantic characteristics.

## Key Features

- **Layout-Centric Organization**: Define complete workspace layouts with rules for each group
- **Template System**: Quick setup with predefined layouts for common workflows
- **Semantic Detection**: 17 semantic types for intelligent file categorization
- **Project-Specific**: Each project can have its own layout configurations
- **Automatic Placement**: Tabs placed automatically based on your layout rules
- **Manual Controls**: Commands for layout management and debugging

## Quick Start

**This plugin requires project-specific setup to function.**

### 1. Install Plugin
1. Copy the `Jorn_AutoPlaceTabs` folder to your Sublime Text `Packages` directory
2. Restart Sublime Text

### 2. Set Up Your First Layout
1. Open a project folder in Sublime Text
2. Open Command Palette (`Ctrl+Shift+P`)
3. Run: `Jorn AutoPlace: Create Layout from Template`
4. Choose a template (e.g., "Web Development Layout")
5. Give it a name (e.g., "my-project")

**That's it!** The plugin will now automatically organize your tabs.

### 3. How It Works
- Open files → Plugin detects file characteristics → Places tabs in appropriate groups
- Example: `src/App.vue` → Group 0 (Source Files), `tests/App.test.js` → Group 1 (Tests)

## Available Commands

### Layout Management
- `Jorn AutoPlace: Create Layout from Template` - Quick setup from templates
- `Jorn AutoPlace: Switch Layout` - Change between configured layouts
- `Jorn AutoPlace: Project Information` - View current project settings

### Tab Placement
- `Jorn AutoPlace: Place Current Tab` - Manually place current tab
- `Jorn AutoPlace: Apply Active Layout` - Reorganize all tabs
- `Jorn AutoPlace: Toggle Auto-Placement` - Enable/disable automatic placement

### Debugging & Info
- `Jorn AutoPlace: Show Semantic Types` - See how plugin categorizes current file
- `Jorn AutoPlace: Show Current Rules` - View active layout rules
- `Jorn AutoPlace: Export Layouts` - Share your layout configurations

## Configuration

**Important**: This plugin is designed to work with project-specific configurations. The global settings file serves as a reference only.

### Project-Specific Configuration (Required)

Layouts are configured in your `.sublime-project` file under `"settings"` → `"jorn_auto_place_tabs"`.

**Recommended approach**: Use the "Create Layout from Template" command instead of manual configuration.

### Manual Configuration Example

If you prefer to configure manually, add this structure to your `.sublime-project` file:

```json
{
    "folders": [{"path": "."}],
    "settings": {
        "jorn_auto_place_tabs": {
            "auto_place_on_activation": true,
            "active_layout": "development",
            "layouts": {
                "development": {
                    "name": "Development Layout",
                    "description": "Main development workflow",
                    "layout_type": "custom",
                    "layout_definition": {
                        "cols": [0.0, 0.5, 1.0],
                        "rows": [0.0, 1.0],
                        "cells": [[0,0,1,1], [1,0,2,1]]
                    },
                    "group_rules": {
                        "0": [{
                            "description": "Source files",
                            "match": {
                                "extensions": [".py", ".js", ".ts"],
                                "directory_patterns": ["*/src/*", "*/lib/*"],
                                "types": ["project", "exists"]
                            }
                        }],
                        "1": [{
                            "description": "External and temporary files",
                            "match": {
                                "types": ["external", "unsaved", "scratch"]
                            }
                        }]
                    }
                }
            }
        }
    }
}
```

**Recommended**: Use `Jorn AutoPlace: Create Layout from Template` instead of manual configuration.

### Global Settings (Reference Only)

The global settings file serves as a reference and has passive defaults:

```json
{
    "auto_place_on_load": false,
    "auto_place_on_activation": false,
    "enable_debug_prints": false,
    "exclude_patterns": [],
    "max_placements_per_second": 20
}
```

**Note**: Global settings are intentionally passive. Active configuration happens in project files.

**Performance Settings**:
- `max_placements_per_second`: Rate limit for tab placements (default: 20). Increase for large projects or fast workflows. When exceeded, shows status message and temporarily skips placements.
- `large_file_threshold`: Skip content analysis for files larger than this (default: 1MB). Prevents UI freezing on large files.
- `project_file_check_interval`: Seconds between project file modification checks (default: 2.0). Increase to 5.0+ for network drives to reduce I/O. Uses adaptive intervals that increase automatically on file access errors.

### Available Templates

The plugin includes several predefined templates:

- **Web Development**: Frontend workflow with source, tests, styles, temp, and external groups
- **Python Data Science**: Python projects with code, notebooks, data, docs, and external groups
- **General Development**: Generic layout for any programming project
- **Documentation**: Writing-focused layout for documentation projects

### Semantic Types

The plugin automatically detects 17 semantic types for intelligent placement:

**Basic States**: `unsaved`, `dirty`, `saved`, `scratch`, `readonly`
**Location**: `project`, `external`
**Existence**: `deleted`, `missing`, `exists`
**Content**: `empty`, `non_empty`
**Activity**: `active`, `inactive`, `visible`, `background`
**Time-based**: `recently_opened`, `short_lived`, `inactive_file`, `stale`
**Size-based**: `large_file`, `small_file`
**Syntax**: `has_syntax`, `plain_text`

### Rule Matching

Rules use `match` and `exclude` conditions:

```json
{
    "match": {
        "extensions": [".py", ".js"],
        "directory_patterns": ["*/src/*"],
        "file_name_patterns": ["*.test.*"],
        "types": ["project", "exists"]
    },
    "exclude": {
        "types": ["deleted", "empty"]
    }
}
```

**Cross-Platform Compatibility**: All file paths are normalized to use forward slashes for pattern matching, ensuring consistent behavior on Windows, macOS, and Linux.

**Pattern Types**:
- `extensions`: File extensions (case-insensitive)
- `directory_patterns`: Unix-style glob patterns for full paths
- `file_name_patterns`: Glob patterns for filenames only
- `types`: Semantic types from the 17 available types

## Troubleshooting

### Plugin Not Working
1. Ensure you've created a layout using "Create Layout from Template"
2. Check that `auto_place_on_activation` is enabled in your project settings
3. Use "Show Semantic Types" to debug file categorization

### No Layouts Available
- Run "Create Layout from Template" to set up your first layout
- The plugin requires project-specific configuration to function

### Tabs Not Placing Correctly
- Use "Show Current Rules" to view active layout rules
- Use "Show Semantic Types" to see how files are categorized
- Check exclude patterns in your configuration

### Rate Limiting Messages
If you see "AutoPlace: Rate limited" in the status bar:
- This prevents performance issues during rapid file operations
- Increase `max_placements_per_second` in settings for faster workflows
- Default is 20 placements per second, which handles most use cases

### Error Messages
The plugin shows user-friendly error messages in the status bar:
- **"AutoPlace Error"**: Critical issues that prevent functionality
- **"AutoPlace Warning"**: Non-critical issues like missing groups or file access problems
- **Network Drive Issues**: Automatic adaptive intervals reduce I/O on problematic drives

### Pattern Matching Issues
If files aren't matching your patterns correctly:
- Use forward slashes in all patterns, even on Windows: `"*/src/*"` not `"*\\src\\*"`
- Test patterns with "Show Semantic Types" to see how files are categorized
- Remember that `directory_patterns` match the full file path
- Use `file_name_patterns` for filename-only matching

### Tab Sorting Issues
If alphabetical sorting seems inconsistent:
- The plugin sorts by `(directory, filename)` for predictable ordering
- Files in different directories with the same name will sort by directory first
- Example: `src/index.ts` comes before `tests/index.ts`

## Architecture

Modern, modular design with enterprise-grade code quality:

### Core Components
- **Rule Engine**: Centralized rule matching and placement decisions
- **Layout Manager**: Handles layout definitions and application
- **Semantic Detector**: Categorizes files using 17 semantic types
- **Project Integration**: Manages project-specific settings and templates
- **Event Listener**: Handles all Sublime Text events for automatic placement
- **Command System**: User-facing commands for manual operations

### Code Quality Features
- **Type Hints**: Full Python 3.8+ type annotations for better IDE support
- **Modular Design**: Separated concerns into focused modules
- **Centralized Logging**: Proper Sublime Text logging integration
- **Single Source of Truth**: Eliminated code duplication
- **Performance Optimized**: Smart caching and concurrency protection

### Performance Features

- **Settings Caching**: Project settings and data cached per window for performance
- **Automatic Cache Invalidation**: Cache cleared when:
  - Project files are saved (`.sublime-project` files)
  - Project files are modified externally
  - Project is switched or closed
  - Layout settings are changed programmatically
- **File Change Detection**: Monitors project file modification times
- **Memory Management**: Cache automatically cleaned up on window close
- **Concurrency Safety**: Per-view locks prevent concurrent placement operations
- **Optimized I/O**: Reduced `window.project_data()` calls through caching
