from __future__ import annotations
import sublime
import sublime_plugin
import os
import time
import fnmatch
from collections import defaultdict, deque
from typing import Optional, Dict, Any, Set

# Import layout-centric classes
from .lib.layout_definition import LayoutDefinition, LayoutManager
from .lib.semantic_detector import SemanticTypeDetector
from .lib.layout_templates import LayoutTemplates
from .lib.project_integration import ProjectIntegration
from .lib.rule_engine import RuleEngine
from .lib.event_listener import AutoPlaceEventListener
from .lib.logging_utils import init_logger, get_logger

PLUGIN_NAME = "Jorn_AutoPlaceTabs"

# Settings key constants for maintainability
class SettingsKeys:
    AUTO_PLACE_ON_ACTIVATION = "auto_place_on_activation"
    AUTO_PLACE_ON_LOAD = "auto_place_on_load"
    ENABLE_DEBUG_PRINTS = "enable_debug_prints"
    GROUP_SORT_METHOD = "group_sort_method"
    EXCLUDE_PATTERNS = "exclude_patterns"
    MAX_PLACEMENTS_PER_SECOND = "max_placements_per_second"
    LARGE_FILE_THRESHOLD = "large_file_threshold"
    PROJECT_FILE_CHECK_INTERVAL = "project_file_check_interval"
    ACTIVE_LAYOUT = "active_layout"
    LAYOUTS = "layouts"


class Jorn_AutoPlaceTabsCommand(sublime_plugin.EventListener):
    """
    Main plugin that automatically places tabs in appropriate groups based on:
    - File type/extension patterns
    - Directory patterns  
    - Project membership
    - Custom user-defined rules
    
    Prevents infinite loops via:
    1) Recursion guard (_is_placing)
    2) Frequency-based limit (MAX_PLACEMENTS_PER_SECOND)
    3) Placement history tracking
    """

    _instance = None

    def __init__(self):
        super().__init__()

        # Initialize logger
        self.logger = init_logger(PLUGIN_NAME)

        self.settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")
        self.settings.add_on_change("jorn_auto_place_tabs", self._on_settings_changed)
        self._placement_locks = set()  # Per-view placement locks (view.id())
        self._placement_timestamps = defaultdict(deque)  # Per-window rate limiting
        self._last_placements = defaultdict(lambda: defaultdict(tuple))
        self._project_settings_cache = {}  # Cache project settings by window ID
        self._project_data_cache = {}  # Cache project data by window ID
        self._project_file_timestamps = {}  # Track project file modification times
        self._last_mtime_check = {}  # Track when we last checked mtime for each window
        self._file_access_errors = {}  # Track file access errors for adaptive intervals

        # Layout-centric components
        self.layout_manager = LayoutManager(self)
        self.semantic_detector = SemanticTypeDetector(self)
        self.project_integration = ProjectIntegration(self)
        self.rule_engine = RuleEngine(self)

        Jorn_AutoPlaceTabsCommand._instance = self

        # Set up event listener
        self._setup_event_listener()

    def _on_settings_changed(self):
        """Called when settings file changes."""
        self._clear_settings_cache()
        self._debug_print("Settings reloaded due to file change")

    @classmethod
    def instance(cls):
        """Used by manual placement commands to reference this plugin instance."""
        return cls._instance

    def _debug_print(self, message: str) -> None:
        """Print debug message only if debug mode is enabled."""
        self.logger.set_debug_enabled(self.settings.get("enable_debug_prints", False))
        self.logger.debug(message)

    def _user_error(self, message: str, show_dialog: bool = False) -> None:
        """Show error to user via status bar or dialog."""
        self.logger.user_error(message, show_dialog)

    def _user_warning(self, message: str) -> None:
        """Show warning to user via status bar."""
        self.logger.user_warning(message)

    def _setup_event_listener(self) -> None:
        """Set up the event listener with plugin reference."""
        # The event listener is automatically registered by Sublime when the module loads
        # We just need to ensure it has a reference to this plugin instance
        pass  # Event listener will get plugin reference through instance() method

    def _get_effective_settings(self, window):
        """Get effective settings combining global and project-specific settings."""
        if not window:
            return self.settings

        window_id = window.id()

        # Check if project file has changed
        if self._has_project_file_changed(window):
            self._clear_settings_cache(window_id)

        # Check cache first
        if window_id in self._project_settings_cache:
            return self._project_settings_cache[window_id]

        # Start with global settings
        effective_settings = {}

        # Copy layout-centric settings only (using constants for maintainability)
        settings_keys = [
            SettingsKeys.AUTO_PLACE_ON_ACTIVATION,
            SettingsKeys.AUTO_PLACE_ON_LOAD,
            SettingsKeys.ENABLE_DEBUG_PRINTS,
            SettingsKeys.GROUP_SORT_METHOD,
            SettingsKeys.EXCLUDE_PATTERNS,
            SettingsKeys.MAX_PLACEMENTS_PER_SECOND,
            SettingsKeys.LARGE_FILE_THRESHOLD,
            SettingsKeys.PROJECT_FILE_CHECK_INTERVAL
        ]
        for key in settings_keys:
            effective_settings[key] = self.settings.get(key)

        # Get project-specific settings (with caching)
        project_data = self._get_cached_project_data(window)
        if project_data and "settings" in project_data:
            project_settings = project_data["settings"].get("jorn_auto_place_tabs", {})
            if project_settings:
                self._debug_print(f"Found project-specific settings: {list(project_settings.keys())}")
                # Override global settings with project-specific ones
                effective_settings.update(project_settings)

        # Cache the result
        self._project_settings_cache[window_id] = effective_settings

        return effective_settings

    def _get_cached_project_data(self, window):
        """Get project data with caching to reduce repeated calls."""
        window_id = window.id()

        # Check if we have cached project data
        if window_id in self._project_data_cache:
            return self._project_data_cache[window_id]

        # Get fresh project data and cache it
        project_data = window.project_data()
        self._project_data_cache[window_id] = project_data
        return project_data

    def _has_project_file_changed(self, window):
        """Check if the project file has been modified since last cache (throttled)."""
        project_data = self._get_cached_project_data(window)
        if not project_data:
            return False

        project_file_name = window.project_file_name()
        if not project_file_name:
            return False

        window_id = window.id()
        now = time.time()

        # Throttle mtime checks to avoid expensive I/O on every activation
        base_interval = self._get_setting(SettingsKeys.PROJECT_FILE_CHECK_INTERVAL, 2.0, window)

        # Adaptive interval: increase if we've had recent I/O errors (network drives)
        adaptive_interval = base_interval
        if hasattr(self, '_file_access_errors'):
            error_count = self._file_access_errors.get(window_id, 0)
            if error_count > 0:
                # Exponential backoff for problematic files/drives
                adaptive_interval = min(base_interval * (2 ** error_count), 30.0)  # Max 30 seconds

        last_check = self._last_mtime_check.get(window_id, 0)
        if now - last_check < adaptive_interval:
            return False  # Skip check, too soon since last check

        self._last_mtime_check[window_id] = now

        try:
            # Only do expensive file system call if enough time has passed
            if not os.path.exists(project_file_name):
                return False

            current_mtime = os.path.getmtime(project_file_name)
            cached_mtime = self._project_file_timestamps.get(window_id)

            if cached_mtime is None or current_mtime > cached_mtime:
                # Update timestamp and reset error count on success
                self._project_file_timestamps[window_id] = current_mtime
                if hasattr(self, '_file_access_errors'):
                    self._file_access_errors[window_id] = 0  # Reset error count on success
                self._debug_print(f"Project file changed detected: {project_file_name}")
                return cached_mtime is not None  # Don't invalidate on first check

            # Reset error count on successful access
            if hasattr(self, '_file_access_errors'):
                self._file_access_errors[window_id] = 0
            return False
        except (OSError, IOError) as e:
            # File access error (network drive timeout, etc.) - track for adaptive intervals
            if not hasattr(self, '_file_access_errors'):
                self._file_access_errors = {}
            self._file_access_errors[window_id] = self._file_access_errors.get(window_id, 0) + 1

            error_msg = f"Project file access error (attempt {self._file_access_errors[window_id]}): {str(e)}"
            self._user_warning(error_msg)
            return True

    def _normalize_path(self, file_path):
        """Normalize file path for consistent pattern matching across platforms."""
        if not file_path:
            return file_path
        # Convert backslashes to forward slashes for consistent pattern matching
        return file_path.replace("\\", "/")

    def _clear_settings_cache(self, window_id=None):
        """Clear settings cache for a specific window or all windows."""
        if window_id:
            self._project_settings_cache.pop(window_id, None)
            self._project_data_cache.pop(window_id, None)
            self._project_file_timestamps.pop(window_id, None)
            self._last_mtime_check.pop(window_id, None)
            self._placement_timestamps.pop(window_id, None)
            if hasattr(self, '_file_access_errors'):
                self._file_access_errors.pop(window_id, None)
            if hasattr(self, '_last_rate_limit_warning'):
                self._last_rate_limit_warning.pop(window_id, None)
        else:
            self._project_settings_cache.clear()
            self._project_data_cache.clear()
            self._project_file_timestamps.clear()
            self._last_mtime_check.clear()
            self._placement_timestamps.clear()
            if hasattr(self, '_file_access_errors'):
                self._file_access_errors.clear()
            if hasattr(self, '_last_rate_limit_warning'):
                self._last_rate_limit_warning.clear()

    def _get_setting(self, key, default=None, window=None):
        """Get a setting value from effective settings (global + project-specific)."""
        if window:
            effective_settings = self._get_effective_settings(window)
            return effective_settings.get(key, default)
        else:
            return self.settings.get(key, default)

    def _get_project_settings(self, window: sublime.Window) -> Dict[str, Any]:
        """Get project-specific settings with caching."""
        window_id = window.id()

        # Check if project file has changed
        if self._has_project_file_changed(window):
            self._clear_settings_cache(window_id)

        # Return cached settings if available
        if window_id in self._project_settings_cache:
            return self._project_settings_cache[window_id]

        # Load project settings
        project_data = window.project_data()
        project_settings = {}

        if project_data and "settings" in project_data:
            plugin_settings = project_data["settings"].get("jorn_auto_place_tabs", {})
            if isinstance(plugin_settings, dict):
                project_settings = plugin_settings

        # Cache the settings
        self._project_settings_cache[window_id] = project_settings
        return project_settings

    def _has_project_file_changed(self, window: sublime.Window) -> bool:
        """Check if project file has been modified since last check."""
        window_id = window.id()
        project_file_name = window.project_file_name()

        if not project_file_name:
            return False

        # Adaptive interval based on file access errors
        base_interval = self._get_setting("project_file_check_interval", 2.0, window)
        error_count = self._file_access_errors.get(window_id, 0)

        if error_count > 0:
            # Exponential backoff for problematic files/drives
            adaptive_interval = min(base_interval * (2 ** error_count), 30.0)  # Max 30 seconds
        else:
            adaptive_interval = base_interval

        # Check if enough time has passed since last check
        now = time.time()
        last_check = self._last_mtime_check.get(window_id, 0)
        if now - last_check < adaptive_interval:
            return False

        self._last_mtime_check[window_id] = now

        try:
            current_mtime = os.path.getmtime(project_file_name)
            cached_mtime = self._project_file_timestamps.get(window_id)

            if cached_mtime is None or current_mtime > cached_mtime:
                # Update timestamp and reset error count on success
                self._project_file_timestamps[window_id] = current_mtime
                if hasattr(self, '_file_access_errors'):
                    self._file_access_errors[window_id] = 0  # Reset error count on success
                self._debug_print(f"Project file changed detected: {project_file_name}")
                return cached_mtime is not None  # Don't invalidate on first check

            # Reset error count on successful access
            if hasattr(self, '_file_access_errors'):
                self._file_access_errors[window_id] = 0
            return False
        except (OSError, IOError) as e:
            # File access error (network drive timeout, etc.) - track for adaptive intervals
            if not hasattr(self, '_file_access_errors'):
                self._file_access_errors = {}
            self._file_access_errors[window_id] = self._file_access_errors.get(window_id, 0) + 1

            error_msg = f"Project file access error (attempt {self._file_access_errors[window_id]}): {str(e)}"
            self._user_warning(error_msg)
            return True

    def _clear_settings_cache(self, window_id: Optional[int] = None) -> None:
        """Clear settings cache for a specific window or all windows."""
        if window_id:
            self._project_settings_cache.pop(window_id, None)
            self._project_data_cache.pop(window_id, None)
            self._project_file_timestamps.pop(window_id, None)
            self._last_mtime_check.pop(window_id, None)
            self._placement_timestamps.pop(window_id, None)
            if hasattr(self, '_file_access_errors'):
                self._file_access_errors.pop(window_id, None)
            if hasattr(self, '_last_rate_limit_warning'):
                self._last_rate_limit_warning.pop(window_id, None)
        else:
            self._project_settings_cache.clear()
            self._project_data_cache.clear()
            self._project_file_timestamps.clear()
            self._last_mtime_check.clear()
            self._placement_timestamps.clear()
            if hasattr(self, '_file_access_errors'):
                self._file_access_errors.clear()
            if hasattr(self, '_last_rate_limit_warning'):
                self._last_rate_limit_warning.clear()

    def _check_placement_frequency(self, window: sublime.Window) -> bool:
        """Check if placement frequency is within limits."""
        window_id = window.id()
        max_per_second = self._get_setting("max_placements_per_second", 20, window)

        now = time.time()
        timestamps = self._placement_timestamps[window_id]

        # Remove timestamps older than 1 second
        while timestamps and now - timestamps[0] > 1.0:
            timestamps.popleft()

        if len(timestamps) >= max_per_second:
            # Rate limit exceeded
            if not hasattr(self, '_last_rate_limit_warning'):
                self._last_rate_limit_warning = {}

            last_warning = self._last_rate_limit_warning.get(window_id, 0)
            if now - last_warning > 5.0:  # Show warning at most every 5 seconds
                sublime.status_message("AutoPlace: Rate limited")
                self._last_rate_limit_warning[window_id] = now

            return False

        # Add current timestamp
        timestamps.append(now)
        return True

    def _determine_target_group(self, view: sublime.View) -> Optional[int]:
        """Determine the target group for a view using centralized rule engine."""
        return self.rule_engine.determine_target_group(view)

    def _place_tab(self, view: sublime.View, target_group: Optional[int] = None) -> None:
        """Place a tab in the specified group."""
        window = view.window()
        if not window:
            return

        # Use provided target group or determine it
        if target_group is None:
            target_group = self._determine_target_group(view)
            if target_group is None:
                return

        current_group, current_index = window.get_view_index(view)

        # Check if already in correct group
        if current_group == target_group:
            return

        # Check if target group exists
        layout = window.layout()
        num_groups = len(layout.get("cells", []))
        if target_group >= num_groups:
            error_msg = f"Target group {target_group} doesn't exist (only {num_groups} groups)"
            self._user_warning(error_msg)
            return

        # Perform the placement
        try:
            # Use per-view placement locks to prevent race conditions
            view_id = view.id()
            if view_id in self._placement_locks:
                return

            self._placement_locks.add(view_id)

            try:
                # Move the tab
                window.set_view_index(view, target_group, -1)  # -1 = append to end
                self._debug_print(f"Moved tab to group {target_group}: {view.file_name() or view.name()}")
            finally:
                self._placement_locks.discard(view_id)

        except Exception as e:
            self._debug_print(f"Failed to place tab: {e}")

    def _normalize_path(self, path: str) -> str:
        """Normalize file path for consistent matching."""
        return os.path.normpath(path).replace("\\", "/")

    def _get_effective_settings(self, window: sublime.Window) -> Dict[str, Any]:
        """Get effective settings (global + project overrides)."""
        effective = {}

        # Start with global settings
        for key in ["auto_place_on_activation", "auto_place_on_load", "enable_debug_prints",
                   "exclude_patterns", "max_placements_per_second", "large_file_threshold",
                   "project_file_check_interval", "active_layout", "layouts"]:
            effective[key] = self.settings.get(key)

        # Apply project overrides
        project_settings = self._get_project_settings(window)
        effective.update(project_settings)

        return effective

    def _get_view_sort_key(self, view: sublime.View) -> tuple:
        """Get sort key for view ordering."""
        view_name = view.name() or ""
        file_name = view.file_name()

        if file_name:
            return (0, os.path.basename(file_name).lower())
        else:
            return ("", view_name.lower())


# Import command classes from separate module
from .lib.commands import *

# Create event listener instance for Sublime Text to register
event_listener = AutoPlaceEventListener()

    def _check_placement_frequency(self, window=None):
        """Rate limiting to prevent excessive placements (per-window)."""
        # Get configurable rate limit
        max_placements = self._get_setting(SettingsKeys.MAX_PLACEMENTS_PER_SECOND, 20, window)

        # Use window-specific rate limiting
        window_id = window.id() if window else 0
        window_timestamps = self._placement_timestamps[window_id]

        now = time.time()
        window_timestamps.append(now)

        # Remove timestamps older than 1 second
        while (window_timestamps and
               now - window_timestamps[0] > 1.0):
            window_timestamps.popleft()

        current_rate = len(window_timestamps)
        if current_rate > max_placements:
            # Provide user feedback when rate limiting kicks in
            if not hasattr(self, '_last_rate_limit_warning'):
                self._last_rate_limit_warning = {}

            # Only show warning once per second per window to avoid spam
            last_warning = self._last_rate_limit_warning.get(window_id, 0)
            if now - last_warning > 1.0:
                sublime.status_message(f"AutoPlace: Rate limited ({current_rate}/{max_placements} per second)")
                self._last_rate_limit_warning[window_id] = now

            return False

        return True









    def _determine_target_group(self, view: sublime.View) -> Optional[int]:
        """Determine the target group for a view using centralized rule engine."""
        return self.rule_engine.determine_target_group(view)





    def _place_tab(self, view, target_group=None):
        """Place a tab in its target group."""
        # Check if this view is already being placed (per-view concurrency protection)
        view_id = view.id()
        if view_id in self._placement_locks:
            return

        window = view.window()
        if not window:
            return

        # Use provided target group or determine it
        if target_group is None:
            target_group = self._determine_target_group(view)
            if target_group is None:
                return

        current_group, current_index = window.get_view_index(view)

        # Robust group existence check with current state verification
        current_num_groups = window.num_groups()
        if target_group >= current_num_groups:
            error_msg = f"Target group {target_group} doesn't exist (only {current_num_groups} groups)"
            self._user_warning(error_msg)
            return None

        # Double-check that the group still exists (race condition protection)
        if target_group < 0:
            error_msg = f"Invalid target group {target_group} (negative)"
            self._user_warning(error_msg)
            return None

        self._debug_print(f"Current group: {current_group}, Target group: {target_group}")
        if current_group == target_group:
            self._debug_print(f"Tab already in target group {target_group}, skipping")
            return

        # Acquire per-view lock
        view_id = view.id()
        self._placement_locks.add(view_id)
        try:
            # Determine target index within group
            target_index = self._get_target_index(view, target_group, window)

            self._debug_print(f"Moving tab from group {current_group} to group {target_group}, index {target_index}")
            window.set_view_index(view, target_group, target_index)

            # Track this placement
            self._last_placements[window.id()][view.id()] = (target_group, target_index, time.time())

        finally:
            # Release per-view lock
            self._placement_locks.discard(view_id)

    def _get_target_index(self, view, target_group, window=None):
        """Determine the target index within a group."""
        if not window:
            window = view.window()
        views_in_group = window.views_in_group(target_group)

        sort_method = self._get_setting(SettingsKeys.GROUP_SORT_METHOD, "append", window)

        if sort_method == "prepend":
            return 0
        elif sort_method == "append":
            return len(views_in_group)
        elif sort_method == "alphabetical":
            return self._get_alphabetical_index(view, views_in_group)
        else:
            return len(views_in_group)

    def _get_alphabetical_index(self, view, views_in_group):
        """Get index for alphabetical insertion with directory-aware sorting."""
        view_sort_key = self._get_view_sort_key(view)

        for i, existing_view in enumerate(views_in_group):
            existing_sort_key = self._get_view_sort_key(existing_view)
            if view_sort_key < existing_sort_key:
                return i

        return len(views_in_group)

    def _get_view_sort_key(self, view):
        """Get sort key for a view that considers directory structure."""
        file_path = view.file_name()
        if file_path:
            # Normalize path for consistent sorting
            normalized_path = self._normalize_path(file_path)
            # Split into directory and filename for proper sorting
            directory = os.path.dirname(normalized_path)
            filename = os.path.basename(normalized_path)
            # Sort by (directory, filename) tuple for predictable ordering
            return (directory.lower(), filename.lower())
        else:
            # For unsaved files, use view name with empty directory
            view_name = view.name() or "untitled"
            return ("", view_name.lower())


# Import command classes from separate module
from .lib.commands import *

# Create event listener instance for Sublime Text to register
event_listener = AutoPlaceEventListener()
        if not plugin:
            return

        # Use layout-centric approach only
        layout_def = plugin.layout_manager.get_active_layout(self.window)
        if layout_def:
            # Apply the complete layout
            success = plugin.layout_manager.apply_layout(self.window, layout_def)
            if success:
                sublime.status_message(f"Applied layout: {layout_def.get_display_name()}")
            else:
                sublime.error_message("Failed to apply layout")
        else:
            sublime.error_message("No active layout configured. Use 'Create Layout from Template' or 'Switch Layout' first.")


class JornAutoPlaceTabsToggleCommand(sublime_plugin.WindowCommand):
    """Toggle auto-placement on/off."""

    def run(self):
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            return

        current = plugin.settings.get("auto_place_on_activation", True)
        plugin.settings.set("auto_place_on_activation", not current)
        sublime.save_settings(f"{PLUGIN_NAME}.sublime-settings")

        status = "enabled" if not current else "disabled"
        sublime.status_message(f"Auto-placement {status}")


class JornAutoPlaceTabsReloadSettingsCommand(sublime_plugin.WindowCommand):
    """Reload plugin settings and clear project settings cache."""

    def run(self):
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            return

        plugin.settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")
        plugin._clear_settings_cache()  # Clear all cached project settings
        sublime.status_message("AutoPlace settings reloaded")


class JornAutoPlaceTabsShowRulesCommand(sublime_plugin.WindowCommand):
    """Show current placement rules in a new view."""

    def run(self):
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            return

        view = self.window.new_file()
        view.set_name("AutoPlace Rules")
        view.set_scratch(True)

        rules_text = self._format_rules(plugin.settings)
        view.run_command("append", {"characters": rules_text})
        view.set_read_only(True)

    def _format_rules(self, settings):
        """Format current rules for display."""
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            return "Plugin not available"

        # Get effective settings for this window
        effective_settings = plugin._get_effective_settings(self.window)

        lines = ["# Jorn AutoPlace Tabs - Current Rules\n\n"]

        # Check if project-specific settings are active
        project_data = self.window.project_data()
        has_project_settings = (project_data and "settings" in project_data and
                               "jorn_auto_place_tabs" in project_data["settings"])

        if has_project_settings:
            lines.append("## Project-Specific Settings Active\n")
            project_settings = project_data["settings"]["jorn_auto_place_tabs"]
            lines.append(f"Project overrides: {', '.join(project_settings.keys())}\n\n")
        else:
            lines.append("## Using Global Settings Only\n\n")

        # Auto-placement status
        auto_on_activation = effective_settings.get("auto_place_on_activation", True)
        auto_on_load = effective_settings.get("auto_place_on_load", True)
        lines.append(f"Auto-placement on activation: {auto_on_activation}\n")
        lines.append(f"Auto-placement on load: {auto_on_load}\n\n")

        # Show layout-centric configuration
        lines.append("## Layout-Centric Configuration\n")

        # Check for active layout
        layout_def = plugin.layout_manager.get_active_layout(self.window)
        if layout_def:
            lines.append(f"Active layout: {layout_def.name}\n")
            lines.append(f"Layout type: {layout_def.layout_type}\n")
            lines.append(f"Groups defined: {len(layout_def.group_rules)}\n\n")

            # Show group rules
            lines.append("## Group Rules\n")
            for group_id, rules in layout_def.group_rules.items():
                lines.append(f"**Group {group_id}:**\n")
                for rule in rules:
                    description = rule.get("description", "Unnamed rule")
                    lines.append(f"  - {description}\n")
        else:
            lines.append("No active layout configured.\n")
            lines.append("Use 'Switch Layout' or 'Create from Template' to set up tab placement.\n")

        # Custom layouts
        layout_configs = effective_settings.get("layout_configs", {})
        if layout_configs:
            lines.append("\n## Custom Layouts\n")
            for group_count, layout in layout_configs.items():
                group_count_actual = len(layout.get("cells", []))
                lines.append(f"Groups {group_count}: Custom layout with {group_count_actual} groups\n")

        return "".join(lines)









class JornAutoPlaceTabsSwitchLayoutCommand(sublime_plugin.WindowCommand):
    """Command to switch between available layout definitions."""

    def run(self):
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            sublime.error_message("AutoPlace plugin not available")
            return

        # Get available layouts
        available_layouts = plugin.layout_manager.get_available_layouts(self.window)

        if not available_layouts:
            sublime.error_message("No layouts defined in project settings")
            return

        # Create layout options for quick panel
        layout_options = []
        layout_names = []

        for name, layout_def in available_layouts.items():
            display_name = layout_def.get_display_name()
            description = layout_def.get_description()
            group_count = layout_def.get_group_count()

            if description:
                layout_options.append([display_name, f"{description} ({group_count} groups)"])
            else:
                layout_options.append([display_name, f"{group_count} groups"])

            layout_names.append(name)

        def on_select(index):
            if index >= 0:
                layout_name = layout_names[index]
                layout_def = available_layouts[layout_name]

                try:
                    # Set as active layout
                    plugin.layout_manager.set_active_layout(self.window, layout_name)

                    # Apply the layout
                    success = plugin.layout_manager.apply_layout(self.window, layout_def)

                    if success:
                        sublime.status_message(f"Switched to layout: {layout_def.get_display_name()}")
                    else:
                        sublime.error_message(f"Failed to apply layout: {layout_def.get_display_name()}")

                except Exception as e:
                    sublime.error_message(f"Error switching layout: {str(e)}")

        self.window.show_quick_panel(layout_options, on_select)


class JornAutoPlaceTabsDebugSemanticTypesCommand(sublime_plugin.WindowCommand):
    """Debug command to show semantic types for current tab."""

    def run(self):
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            sublime.error_message("AutoPlace plugin not available")
            return

        view = self.window.active_view()
        if not view:
            sublime.error_message("No active view")
            return

        # Get layout definition if available
        layout_def = plugin.layout_manager.get_active_layout(self.window)

        # Get debug info
        debug_info = plugin.semantic_detector.get_debug_info(view, self.window, layout_def)

        # Format debug output
        output_lines = [
            f"File: {debug_info['file_name']}",
            f"View ID: {debug_info['view_id']}",
            "",
            "Semantic Types:",
        ]

        for semantic_type in sorted(debug_info['semantic_types']):
            output_lines.append(f"  - {semantic_type}")

        output_lines.extend([
            "",
            "Tracking Data:",
            f"  Open Time: {debug_info['tracking_data']['open_time']}",
            f"  Focus Time: {debug_info['tracking_data']['focus_time']}",
            f"  Modified Time: {debug_info['tracking_data']['modified_time']}",
        ])

        if layout_def:
            output_lines.extend([
                "",
                f"Active Layout: {layout_def.get_display_name()}",
                f"Layout Groups: {layout_def.get_group_count()}",
            ])

        # Show in new tab
        output_view = self.window.new_file()
        output_view.set_name("Semantic Types Debug")
        output_view.set_scratch(True)
        output_view.run_command("insert", {"characters": "\n".join(output_lines)})


class JornAutoPlaceTabsCreateLayoutFromTemplateCommand(sublime_plugin.WindowCommand):
    """Command to create a layout from predefined templates."""

    def run(self):
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            sublime.error_message("AutoPlace plugin not available")
            return

        # Get available templates
        template_names = LayoutTemplates.get_template_names()

        # Create options for quick panel
        template_options = []
        template_keys = []

        for key, name, description in template_names:
            template_options.append([name, description])
            template_keys.append(key)

        def on_select(index):
            if index >= 0:
                template_key = template_keys[index]
                template_name = template_names[index][1]

                # Ask for layout name
                def on_layout_name(layout_name):
                    if layout_name:
                        success, message = plugin.project_integration.create_project_layout_from_template(
                            self.window, template_key, layout_name
                        )

                        if success:
                            sublime.status_message(message)
                        else:
                            sublime.error_message(message)

                self.window.show_input_panel(
                    f"Layout name for '{template_name}':",
                    template_key,
                    on_layout_name,
                    None,
                    None
                )

        self.window.show_quick_panel(template_options, on_select)



class JornAutoPlaceTabsProjectInfoCommand(sublime_plugin.WindowCommand):
    """Command to show project information and settings summary."""

    def run(self):
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            sublime.error_message("AutoPlace plugin not available")
            return

        # Get project info
        project_info = plugin.project_integration.get_project_info(self.window)
        summary = plugin.project_integration.get_project_settings_summary(self.window)

        # Format output
        output_lines = [
            "# Jorn AutoPlace Tabs - Project Information\n",
            f"**Project Name:** {summary['project_name']}",
            f"**Has Settings:** {'Yes' if summary['has_settings'] else 'No'}",
            f"**Layout Count:** {summary['layout_count']}",
            f"**Active Layout:** {summary['active_layout'] or 'None'}",
            "",
            "## Project Folders:",
        ]

        for folder in summary['folders']:
            output_lines.append(f"  - {folder}")

        if summary['has_settings']:
            output_lines.extend([
                "",
                "## AutoPlace Settings:",
                f"  - Auto place on activation: {summary.get('auto_place_on_activation', False)}",
                f"  - Auto place on load: {summary.get('auto_place_on_load', False)}",
                f"  - Debug prints: {summary.get('enable_debug_prints', False)}",
            ])

            if project_info['available_layouts']:
                output_lines.extend([
                    "",
                    "## Available Layouts:",
                ])
                for layout_name in project_info['available_layouts']:
                    marker = " (active)" if layout_name == summary['active_layout'] else ""
                    output_lines.append(f"  - {layout_name}{marker}")

        # Show in new tab
        output_view = self.window.new_file()
        output_view.set_name("Project Information")
        output_view.set_scratch(True)
        output_view.run_command("insert", {"characters": "\n".join(output_lines)})
        output_view.set_syntax_file("Packages/Markdown/Markdown.sublime-syntax")


class JornAutoPlaceTabsExportLayoutsCommand(sublime_plugin.WindowCommand):
    """Command to export project layouts to JSON."""

    def run(self):
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            sublime.error_message("AutoPlace plugin not available")
            return

        # Export layouts
        export_data, message = plugin.project_integration.export_project_layouts(self.window)

        if not export_data:
            sublime.error_message(message)
            return

        # Format JSON
        import json
        json_content = json.dumps(export_data, indent=2)

        # Show in new tab
        output_view = self.window.new_file()
        output_view.set_name("Exported Layouts.json")
        output_view.set_scratch(True)
        output_view.run_command("insert", {"characters": json_content})
        output_view.set_syntax_file("Packages/JSON/JSON.sublime-syntax")

        sublime.status_message(message)


class JornAutoPlaceTabsCleanupProjectSettingsCommand(sublime_plugin.WindowCommand):
    """Command to clean up project settings."""

    def run(self):
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            sublime.error_message("AutoPlace plugin not available")
            return

        # Perform cleanup
        success, message = plugin.project_integration.cleanup_project_settings(self.window)

        if success:
            sublime.message_dialog(f"Cleanup completed!\n\n{message}")
        else:
            sublime.error_message(f"Cleanup failed: {message}")


# Create event listener instance for Sublime Text to register
event_listener = AutoPlaceEventListener()
