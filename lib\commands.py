"""
Commands for AutoPlace Tab Plugin

All user-facing commands for manual tab placement and configuration.
"""

from __future__ import annotations
from typing import Optional, List, Tuple
import sublime
import sublime_plugin


class JornAutoPlaceTabsManualCommand(sublime_plugin.WindowCommand):
    """Manual command to place current tab according to rules."""
    
    def run(self) -> None:
        """Execute manual tab placement."""
        view = self.window.active_view()
        if not view:
            return
        
        from ..Jorn_AutoPlaceTabs import Jorn_AutoPlaceTabsCommand
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if plugin:
            target_group = plugin.rule_engine.determine_target_group(view)
            if target_group is not None:
                plugin._place_tab(view, target_group)
                sublime.status_message("Tab placed according to rules")
            else:
                sublime.status_message("No placement rule matched for this tab")


class JornAutoPlaceTabsPlaceAllCommand(sublime_plugin.WindowCommand):
    """Command to place all tabs according to rules."""

    def run(self) -> None:
        """Execute bulk tab placement."""
        from ..Jorn_AutoPlaceTabs import Jorn_AutoPlaceTabsCommand
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            return

        # Use layout-centric approach only
        layout_def = plugin.layout_manager.get_active_layout(self.window)
        if layout_def:
            # Apply the complete layout
            success = plugin.layout_manager.apply_layout(self.window, layout_def)
            if success:
                sublime.status_message(f"Applied layout: {layout_def.get_display_name()}")
            else:
                sublime.error_message("Failed to apply layout")
        else:
            sublime.error_message("No active layout configured. Use 'Create Layout from Template' or 'Switch Layout' first.")


class JornAutoPlaceTabsToggleCommand(sublime_plugin.WindowCommand):
    """Toggle auto-placement on/off."""

    def run(self) -> None:
        """Toggle auto-placement setting."""
        from ..Jorn_AutoPlaceTabs import Jorn_AutoPlaceTabsCommand
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            return

        current = plugin._get_setting("auto_place_on_activation", True, self.window)
        # Note: This would need to be implemented to actually toggle the setting
        # For now, just show current status
        status = "enabled" if current else "disabled"
        sublime.status_message(f"Auto-placement {status}")


class JornAutoPlaceTabsReloadSettingsCommand(sublime_plugin.WindowCommand):
    """Reload plugin settings and clear project settings cache."""

    def run(self) -> None:
        """Reload all plugin settings."""
        from ..Jorn_AutoPlaceTabs import Jorn_AutoPlaceTabsCommand, PLUGIN_NAME
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            return

        plugin.settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")
        plugin._clear_settings_cache()  # Clear all cached project settings
        sublime.status_message("AutoPlace settings reloaded")


class JornAutoPlaceTabsShowRulesCommand(sublime_plugin.WindowCommand):
    """Show current placement rules in a new view."""

    def run(self) -> None:
        """Display current rules in a new view."""
        from ..Jorn_AutoPlaceTabs import Jorn_AutoPlaceTabsCommand
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            return

        view = self.window.new_file()
        view.set_name("AutoPlace Rules")
        view.set_scratch(True)

        rules_text = self._format_rules(plugin.settings)
        view.run_command("append", {"characters": rules_text})
        view.set_read_only(True)

    def _format_rules(self, settings) -> str:
        """Format current rules for display."""
        from ..Jorn_AutoPlaceTabs import Jorn_AutoPlaceTabsCommand
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            return "Plugin not available"

        # Get effective settings for this window
        effective_settings = plugin._get_effective_settings(self.window)

        lines = ["# Jorn AutoPlace Tabs - Current Rules\n\n"]

        # Check if project-specific settings are active
        project_data = self.window.project_data()
        has_project_settings = (project_data and "settings" in project_data and
                               "jorn_auto_place_tabs" in project_data["settings"])

        if has_project_settings:
            lines.append("## Project-Specific Settings Active\n")
            project_settings = project_data["settings"]["jorn_auto_place_tabs"]
            lines.append(f"Project overrides: {', '.join(project_settings.keys())}\n\n")
        else:
            lines.append("## Using Global Settings Only\n\n")

        # Auto-placement status
        auto_on_activation = effective_settings.get("auto_place_on_activation", True)
        auto_on_load = effective_settings.get("auto_place_on_load", True)
        lines.append(f"Auto-placement on activation: {auto_on_activation}\n")
        lines.append(f"Auto-placement on load: {auto_on_load}\n\n")

        # Show layout-centric configuration
        lines.append("## Layout-Centric Configuration\n")

        # Check for active layout
        layout_def = plugin.layout_manager.get_active_layout(self.window)
        if layout_def:
            lines.append(f"Active layout: {layout_def.name}\n")
            lines.append(f"Layout type: {layout_def.layout_type}\n")
            lines.append(f"Groups defined: {len(layout_def.group_rules)}\n\n")

            # Show group rules
            lines.append("## Group Rules\n")
            for group_id, rules in layout_def.group_rules.items():
                lines.append(f"**Group {group_id}:**\n")
                for rule in rules:
                    description = rule.get("description", "Unnamed rule")
                    lines.append(f"  - {description}\n")
        else:
            lines.append("No active layout configured.\n")
            lines.append("Use 'Switch Layout' or 'Create from Template' to set up tab placement.\n")

        # Custom layouts
        layout_configs = effective_settings.get("layout_configs", {})
        if layout_configs:
            lines.append("\n## Custom Layouts\n")
            for group_count, layout in layout_configs.items():
                group_count_actual = len(layout.get("cells", []))
                lines.append(f"Groups {group_count}: Custom layout with {group_count_actual} groups\n")

        return "".join(lines)


class JornAutoPlaceTabsSwitchLayoutCommand(sublime_plugin.WindowCommand):
    """Command to switch between available layout definitions."""

    def run(self) -> None:
        """Show layout selection dialog."""
        from ..Jorn_AutoPlaceTabs import Jorn_AutoPlaceTabsCommand
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            sublime.error_message("AutoPlace plugin not available")
            return

        # Get available layouts
        layout_configs = plugin.layout_manager.get_available_layouts(self.window)
        if not layout_configs:
            sublime.error_message("No layouts available. Use 'Create Layout from Template' first.")
            return

        # Prepare options for quick panel
        layout_options = []
        layout_keys = []

        for layout_name, layout_def in layout_configs.items():
            display_name = layout_def.get_display_name()
            description = layout_def.description or "No description"
            layout_options.append([display_name, description])
            layout_keys.append(layout_name)

        def on_select(index: int) -> None:
            if index >= 0:
                selected_layout = layout_keys[index]
                try:
                    success = plugin.layout_manager.set_active_layout(self.window, selected_layout)
                    if success:
                        sublime.status_message(f"Switched to layout: {layout_options[index][0]}")
                    else:
                        sublime.error_message("Failed to switch layout")
                except Exception as e:
                    sublime.error_message(f"Error switching layout: {str(e)}")

        self.window.show_quick_panel(layout_options, on_select)


class JornAutoPlaceTabsDebugSemanticTypesCommand(sublime_plugin.WindowCommand):
    """Debug command to show semantic types for current tab."""

    def run(self) -> None:
        """Show semantic types for current view."""
        from ..Jorn_AutoPlaceTabs import Jorn_AutoPlaceTabsCommand
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            sublime.error_message("AutoPlace plugin not available")
            return

        view = self.window.active_view()
        if not view:
            sublime.error_message("No active view")
            return

        # Get semantic types
        layout_def = plugin.layout_manager.get_active_layout(self.window)
        semantic_types = plugin.semantic_detector.get_semantic_types(view, self.window, layout_def)

        # Format output
        output_lines = [
            "# Semantic Types Debug",
            "",
            f"**File**: {view.file_name() or view.name() or 'Untitled'}",
            f"**View ID**: {view.id()}",
            "",
            "## Detected Semantic Types:",
            ""
        ]

        if semantic_types:
            for semantic_type in sorted(semantic_types):
                output_lines.append(f"- {semantic_type}")
        else:
            output_lines.append("- No semantic types detected")

        # Show in new view
        output_view = self.window.new_file()
        output_view.set_name("Semantic Types Debug")
        output_view.set_scratch(True)
        output_view.run_command("insert", {"characters": "\n".join(output_lines)})


class JornAutoPlaceTabsCreateLayoutFromTemplateCommand(sublime_plugin.WindowCommand):
    """Command to create a layout from predefined templates."""

    def run(self) -> None:
        """Show template selection dialog."""
        from ..Jorn_AutoPlaceTabs import Jorn_AutoPlaceTabsCommand
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            sublime.error_message("AutoPlace plugin not available")
            return

        # Get available templates
        from ..lib.layout_templates import LayoutTemplates
        templates = LayoutTemplates.get_all_templates()

        if not templates:
            sublime.error_message("No templates available")
            return

        # Prepare options for quick panel
        template_options = []
        template_keys = []

        for template_name, template_data in templates.items():
            description = template_data.get("description", "No description")
            template_options.append([template_name, description])
            template_keys.append(template_name)

        def on_select(index: int) -> None:
            if index >= 0:
                selected_template = template_keys[index]
                # Show input panel for layout name
                def on_name_input(layout_name: str) -> None:
                    if layout_name:
                        try:
                            success = plugin.project_integration.create_layout_from_template(
                                self.window, selected_template, layout_name
                            )
                            if success:
                                sublime.status_message(f"Created layout '{layout_name}' from template")
                            else:
                                sublime.error_message("Failed to create layout")
                        except Exception as e:
                            sublime.error_message(f"Error creating layout: {str(e)}")

                self.window.show_input_panel(
                    "Layout name:",
                    selected_template.lower().replace(" ", "_"),
                    on_name_input,
                    None,
                    None
                )

        self.window.show_quick_panel(template_options, on_select)


class JornAutoPlaceTabsProjectInfoCommand(sublime_plugin.WindowCommand):
    """Command to show project information and settings summary."""

    def run(self) -> None:
        """Show project information."""
        from ..Jorn_AutoPlaceTabs import Jorn_AutoPlaceTabsCommand
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            sublime.error_message("AutoPlace plugin not available")
            return

        # Get project information
        project_info = plugin.project_integration.get_project_info(self.window)

        # Format output
        output_lines = [
            "# AutoPlace Project Information",
            "",
            f"**Project**: {project_info.get('project_name', 'Unknown')}",
            f"**Active Layout**: {project_info.get('active_layout', 'None')}",
            "",
            "## Available Layouts:",
            ""
        ]

        layouts = project_info.get('layouts', {})
        if layouts:
            for layout_name, layout_data in layouts.items():
                display_name = layout_data.get('name', layout_name)
                description = layout_data.get('description', 'No description')
                output_lines.append(f"- **{display_name}**: {description}")
        else:
            output_lines.append("- No layouts configured")

        # Show in new view
        output_view = self.window.new_file()
        output_view.set_name("AutoPlace Project Info")
        output_view.set_scratch(True)
        output_view.run_command("insert", {"characters": "\n".join(output_lines)})
        output_view.set_syntax_file("Packages/Markdown/Markdown.sublime-syntax")


class JornAutoPlaceTabsExportLayoutsCommand(sublime_plugin.WindowCommand):
    """Command to export project layouts to JSON."""

    def run(self) -> None:
        """Export layouts to JSON."""
        from ..Jorn_AutoPlaceTabs import Jorn_AutoPlaceTabsCommand
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            sublime.error_message("AutoPlace plugin not available")
            return

        # Export layouts
        success, export_data, message = plugin.project_integration.export_project_layouts(self.window)

        if not success:
            sublime.error_message(f"Export failed: {message}")
            return

        # Format JSON
        import json
        json_content = json.dumps(export_data, indent=2)

        # Show in new tab
        output_view = self.window.new_file()
        output_view.set_name("Exported Layouts.json")
        output_view.set_scratch(True)
        output_view.run_command("insert", {"characters": json_content})
        output_view.set_syntax_file("Packages/JSON/JSON.sublime-syntax")

        sublime.status_message(message)


class JornAutoPlaceTabsCleanupProjectSettingsCommand(sublime_plugin.WindowCommand):
    """Command to clean up project settings."""

    def run(self) -> None:
        """Clean up invalid project settings."""
        from ..Jorn_AutoPlaceTabs import Jorn_AutoPlaceTabsCommand
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            sublime.error_message("AutoPlace plugin not available")
            return

        # Perform cleanup
        success, message = plugin.project_integration.cleanup_project_settings(self.window)

        if success:
            sublime.message_dialog(f"Cleanup completed!\n\n{message}")
        else:
            sublime.error_message(f"Cleanup failed: {message}")
