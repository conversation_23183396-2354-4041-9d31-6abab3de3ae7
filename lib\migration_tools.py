"""
Migration Tools for Jorn_AutoPlaceTabs

Provides migration utilities from legacy group_rules to layout-centric format.
"""

import sublime
import json


class MigrationTools:
    """Tools for migrating from legacy formats to layout-centric format."""
    
    def __init__(self, plugin):
        """Initialize with reference to main plugin."""
        self.plugin = plugin
    
    def detect_legacy_format(self, window):
        """Detect if window has legacy group_rules format."""
        project_settings = self.plugin._get_effective_settings(window)
        
        # Check for legacy group_rules
        has_group_rules = "group_rules" in project_settings and project_settings["group_rules"]
        
        # Check for layout-centric format
        has_layouts = any(
            key != "_settings" and isinstance(value, dict) and "layout" in value
            for key, value in project_settings.items()
        )
        
        return {
            "has_legacy": has_group_rules,
            "has_layouts": has_layouts,
            "needs_migration": has_group_rules and not has_layouts
        }
    
    def migrate_group_rules_to_layout(self, group_rules, layout_name="migrated_layout"):
        """Convert legacy group_rules to layout-centric format."""
        if not group_rules:
            return None
        
        # Determine number of groups needed
        max_group = max(int(group_id) for group_id in group_rules.keys())
        num_groups = max_group + 1
        
        # Generate layout structure
        layout_structure = self._generate_layout_for_groups(num_groups)
        
        # Convert rules
        converted_rules = {}
        for group_id, rules_list in group_rules.items():
            if rules_list:  # Only include groups with rules
                # Take the first rule if multiple rules exist
                rule = rules_list[0] if isinstance(rules_list, list) else rules_list
                converted_rules[group_id] = {
                    "name": rule.get("description", f"Group {group_id}"),
                    "description": rule.get("description", ""),
                    "match": rule.get("match", {}),
                    "exclude": rule.get("exclude", {})
                }
        
        # Create layout definition
        layout_def = {
            "metadata": {
                "name": "Migrated Layout",
                "description": "Automatically migrated from legacy group_rules format",
                "author": "Migration Tool",
                "tags": ["migrated", "legacy"]
            },
            "layout": layout_structure,
            "rules": converted_rules,
            "settings": {
                "group_sort_method": "append"
            }
        }
        
        return layout_def
    
    def _generate_layout_for_groups(self, num_groups):
        """Generate a layout structure for the specified number of groups."""
        if num_groups <= 1:
            return {
                "cols": [0.0, 1.0],
                "rows": [0.0, 1.0],
                "cells": [[0, 0, 1, 1]]
            }
        elif num_groups == 2:
            return {
                "cols": [0.0, 0.5, 1.0],
                "rows": [0.0, 1.0],
                "cells": [[0, 0, 1, 1], [1, 0, 2, 1]]
            }
        elif num_groups == 3:
            return {
                "cols": [0.0, 0.33, 0.66, 1.0],
                "rows": [0.0, 1.0],
                "cells": [[0, 0, 1, 1], [1, 0, 2, 1], [2, 0, 3, 1]]
            }
        elif num_groups == 4:
            return {
                "cols": [0.0, 0.5, 1.0],
                "rows": [0.0, 0.5, 1.0],
                "cells": [[0, 0, 1, 1], [1, 0, 2, 1], [0, 1, 1, 2], [1, 1, 2, 2]]
            }
        else:
            # For more than 4 groups, create a grid layout
            return self._create_grid_layout(num_groups)
    
    def _create_grid_layout(self, num_groups):
        """Create a grid layout for many groups."""
        import math
        
        # Calculate grid dimensions
        cols_count = math.ceil(math.sqrt(num_groups))
        rows_count = math.ceil(num_groups / cols_count)
        
        # Generate column positions
        cols = [i / cols_count for i in range(cols_count + 1)]
        
        # Generate row positions
        rows = [i / rows_count for i in range(rows_count + 1)]
        
        # Generate cells
        cells = []
        for i in range(num_groups):
            col = i % cols_count
            row = i // cols_count
            cells.append([col, row, col + 1, row + 1])
        
        return {
            "cols": cols,
            "rows": rows,
            "cells": cells
        }
    
    def migrate_project_settings(self, window):
        """Migrate project settings from legacy to layout-centric format."""
        project_data = window.project_data()
        if not project_data:
            return False, "No project data found"
        
        if "settings" not in project_data:
            project_data["settings"] = {}
        
        if "jorn_auto_place_tabs" not in project_data["settings"]:
            return False, "No AutoPlace settings found"
        
        settings = project_data["settings"]["jorn_auto_place_tabs"]
        
        # Check if migration is needed
        detection = self.detect_legacy_format(window)
        if not detection["needs_migration"]:
            return False, "No migration needed"
        
        # Perform migration
        group_rules = settings.get("group_rules", {})
        migrated_layout = self.migrate_group_rules_to_layout(group_rules)
        
        if not migrated_layout:
            return False, "Failed to migrate group_rules"
        
        # Add migrated layout to settings
        settings["migrated_layout"] = migrated_layout
        
        # Set as active layout
        if "_settings" not in settings:
            settings["_settings"] = {}
        settings["_settings"]["active_layout"] = "migrated_layout"
        
        # Optionally remove old group_rules (commented out for safety)
        # del settings["group_rules"]
        
        # Save project data
        window.set_project_data(project_data)
        
        # Clear cache
        self.plugin._clear_settings_cache(window.id())
        
        return True, "Migration completed successfully"
    
    def create_backup_of_settings(self, window):
        """Create a backup of current project settings."""
        project_data = window.project_data()
        if not project_data or "settings" not in project_data:
            return None
        
        settings = project_data["settings"].get("jorn_auto_place_tabs", {})
        if not settings:
            return None
        
        # Create backup with timestamp
        import time
        timestamp = int(time.time())
        
        backup_data = {
            "timestamp": timestamp,
            "settings": settings.copy()
        }
        
        return backup_data
    
    def get_migration_preview(self, window):
        """Get a preview of what migration would produce."""
        project_settings = self.plugin._get_effective_settings(window)
        group_rules = project_settings.get("group_rules", {})
        
        if not group_rules:
            return None
        
        # Generate preview
        migrated_layout = self.migrate_group_rules_to_layout(group_rules)
        
        if not migrated_layout:
            return None
        
        # Create summary
        preview = {
            "original_groups": len(group_rules),
            "migrated_groups": len(migrated_layout["rules"]),
            "layout_structure": migrated_layout["layout"],
            "rules_summary": []
        }
        
        for group_id, rule in migrated_layout["rules"].items():
            preview["rules_summary"].append({
                "group": int(group_id),
                "name": rule["name"],
                "match_conditions": len(rule.get("match", {})),
                "exclude_conditions": len(rule.get("exclude", {}))
            })
        
        return preview
    
    def validate_migrated_layout(self, layout_def):
        """Validate a migrated layout definition."""
        try:
            from .layout_definition import LayoutDefinition
            # Try to create a LayoutDefinition to validate
            LayoutDefinition("test", layout_def)
            return True, "Layout is valid"
        except Exception as e:
            return False, f"Layout validation failed: {str(e)}"
