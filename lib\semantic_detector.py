"""
Semantic Type Detection for Jorn_AutoPlaceTabs

Provides tab state detection including time-based, size-based,
and activity-based semantic types.
"""

from __future__ import annotations
import sublime
import os
import time
from collections import defaultdict
from typing import Set, Dict, Any, Optional, List


class SemanticTypeDetector:
    """Semantic type detection with time and size awareness."""
    
    def __init__(self, plugin):
        """Initialize with reference to main plugin."""
        self.plugin = plugin
        
        # Time tracking
        self._tab_open_times = {}
        self._tab_focus_times = {}
        self._tab_last_modified = {}
        
        # Activity tracking
        self._active_view_id = None
        
        # Default thresholds (can be overridden by layout settings)
        self.default_thresholds = {
            "recently_opened_threshold": 300,      # 5 minutes
            "short_lived_threshold": 10,           # 10 seconds
            "inactive_file_threshold": 86400,      # 24 hours
            "stale_threshold": 3600,               # 1 hour
            "large_file_threshold": 1048576,       # 1MB
            "small_file_threshold": 1024,          # 1KB
        }
    
    def track_view_opened(self, view):
        """Track when a view is opened."""
        self._tab_open_times[view.id()] = time.time()
        self.plugin._debug_print(f"Tracking opened view: {view.id()}")
    
    def track_view_activated(self, view):
        """Track when a view is activated/focused."""
        self._tab_focus_times[view.id()] = time.time()
        self._active_view_id = view.id()
        self.plugin._debug_print(f"Tracking activated view: {view.id()}")
    
    def track_view_modified(self, view):
        """Track when a view is modified."""
        self._tab_last_modified[view.id()] = time.time()
    
    def cleanup_closed_views(self, window):
        """Clean up tracking data for closed views."""
        open_view_ids = {view.id() for view in window.views()}
        
        # Remove tracking data for closed views
        for view_id in list(self._tab_open_times.keys()):
            if view_id not in open_view_ids:
                self._tab_open_times.pop(view_id, None)
                self._tab_focus_times.pop(view_id, None)
                self._tab_last_modified.pop(view_id, None)
    
    def get_semantic_types(self, view, window, layout_definition=None):
        """Get semantic types for a view."""
        types = []
        
        # Get thresholds from layout or use defaults
        thresholds = self._get_thresholds(layout_definition)
        
        # Basic states
        types.extend(self._get_basic_states(view))
        
        # Location states
        types.extend(self._get_location_states(view, window))
        
        # File existence states
        types.extend(self._get_existence_states(view))
        
        # Content states
        types.extend(self._get_content_states(view))
        
        # Activity states
        types.extend(self._get_activity_states(view, window))
        
        # Time-based states
        types.extend(self._get_time_states(view, thresholds))
        
        # Size-based states
        types.extend(self._get_size_states(view, thresholds))
        
        # Syntax states
        types.extend(self._get_syntax_states(view))
        
        return types
    
    def _get_thresholds(self, layout_definition):
        """Get threshold settings from layout or defaults."""
        if layout_definition:
            thresholds = {}
            for key, default_value in self.default_thresholds.items():
                thresholds[key] = layout_definition.get_threshold(key, default_value)
            return thresholds
        return self.default_thresholds
    
    def _get_basic_states(self, view):
        """Get basic tab states."""
        types = []
        
        if view.file_name() is None:
            types.append("unsaved")
        else:
            types.append("saved")
        
        if view.is_dirty():
            types.append("dirty")
        
        if view.is_scratch():
            types.append("scratch")
        
        if view.is_read_only():
            types.append("readonly")
        
        return types
    
    def _get_location_states(self, view, window):
        """Get location-based states with proper cross-platform path handling."""
        import os
        types = []

        file_path = view.file_name()
        if file_path:
            project_folders = window.folders() if window else []

            # Normalize paths for cross-platform comparison
            normalized_file_path = os.path.normcase(os.path.abspath(file_path))

            is_project_file = False
            for folder in project_folders:
                normalized_folder = os.path.normcase(os.path.abspath(folder))

                # Ensure folder path ends with separator for accurate comparison
                if not normalized_folder.endswith(os.sep):
                    normalized_folder += os.sep

                if normalized_file_path.startswith(normalized_folder):
                    is_project_file = True
                    break

            if is_project_file:
                types.append("project")
            else:
                types.append("external")
        else:
            # Unsaved files are considered external
            types.append("external")
        
        return types
    
    def _get_existence_states(self, view):
        """Get file existence states."""
        types = []
        
        file_path = view.file_name()
        if file_path:
            if os.path.exists(file_path):
                types.append("exists")
            else:
                if os.path.dirname(file_path):
                    types.append("missing")  # Path exists but file doesn't
                else:
                    types.append("deleted")  # File was deleted
        
        return types
    
    def _get_content_states(self, view):
        """Get content-based states with performance optimization for large files."""
        types = []

        file_size = view.size()

        # Performance optimization: skip content analysis for very large files
        large_file_threshold = self.get_threshold("large_file_threshold", 1048576)  # 1MB default

        if file_size > large_file_threshold:
            # For large files, assume non-empty and skip expensive content analysis
            types.append("non_empty")
            if hasattr(self, 'plugin') and self.plugin:
                self.plugin._debug_print(f"Skipping content analysis for large file ({file_size} bytes)")
        elif file_size == 0:
            # Empty file
            types.append("empty")
        else:
            # For reasonable-sized files, sample content to check if empty
            sample_size = min(file_size, 1024)  # Sample first 1KB max
            content = view.substr(sublime.Region(0, sample_size)).strip()

            if len(content) == 0 or len(''.join(content.split())) <= 1:
                types.append("empty")
            else:
                types.append("non_empty")
        
        return types
    
    def _get_activity_states(self, view, window):
        """Get activity-based states."""
        types = []
        
        # Check if this is the active view
        if view.id() == self._active_view_id:
            types.append("active")
        else:
            types.append("inactive")
        
        # Check if view is visible (in current group)
        current_group, _ = window.get_view_index(view)
        active_group = window.active_group()
        
        if current_group == active_group:
            types.append("visible")
        else:
            types.append("background")
        
        return types
    
    def _get_time_states(self, view, thresholds):
        """Get time-based states."""
        types = []
        current_time = time.time()
        view_id = view.id()
        
        # Recently opened
        open_time = self._tab_open_times.get(view_id)
        if open_time and (current_time - open_time) < thresholds["recently_opened_threshold"]:
            types.append("recently_opened")
        
        # Short lived
        if open_time and (current_time - open_time) < thresholds["short_lived_threshold"]:
            types.append("short_lived")
        
        # Stale (not focused recently)
        focus_time = self._tab_focus_times.get(view_id)
        if focus_time and (current_time - focus_time) > thresholds["stale_threshold"]:
            types.append("stale")
        
        # Inactive file (file not modified recently on disk)
        file_path = view.file_name()
        if file_path and os.path.exists(file_path):
            try:
                file_mtime = os.path.getmtime(file_path)
                if (current_time - file_mtime) > thresholds["inactive_file_threshold"]:
                    types.append("inactive_file")
            except OSError:
                pass  # File might be inaccessible
        
        return types
    
    def _get_size_states(self, view, thresholds):
        """Get size-based states."""
        types = []
        
        file_path = view.file_name()
        if file_path and os.path.exists(file_path):
            try:
                file_size = os.path.getsize(file_path)
                
                if file_size > thresholds["large_file_threshold"]:
                    types.append("large_file")
                elif file_size < thresholds["small_file_threshold"]:
                    types.append("small_file")
                    
            except OSError:
                pass  # File might be inaccessible
        
        return types
    
    def _get_syntax_states(self, view):
        """Get syntax-based states."""
        types = []
        
        syntax = view.syntax()
        if syntax:
            syntax_name = syntax.name.lower()
            if syntax_name == "plain text" or "plain" in syntax_name:
                types.append("plain_text")
            else:
                types.append("has_syntax")
        else:
            types.append("plain_text")
        
        return types
    
    def get_debug_info(self, view, window, layout_definition=None):
        """Get debug information about semantic type detection."""
        semantic_types = self.get_semantic_types(view, window, layout_definition)
        
        debug_info = {
            "view_id": view.id(),
            "file_name": view.file_name() or "Unsaved",
            "semantic_types": semantic_types,
            "tracking_data": {
                "open_time": self._tab_open_times.get(view.id()),
                "focus_time": self._tab_focus_times.get(view.id()),
                "modified_time": self._tab_last_modified.get(view.id()),
            }
        }
        
        return debug_info
