"""
Project Integration Tools for Jorn_AutoPlaceTabs

Provides project integration, settings management, and template creation.
"""

import sublime
import json
import os


class ProjectIntegration:
    """Project integration and settings management."""
    
    def __init__(self, plugin):
        """Initialize with reference to main plugin."""
        self.plugin = plugin
    
    def get_project_info(self, window):
        """Get project information."""
        project_data = window.project_data()
        
        info = {
            "has_project": project_data is not None,
            "project_name": None,
            "project_path": None,
            "folders": [],
            "has_autoplace_settings": False,
            "autoplace_settings": {},
            "available_layouts": [],
            "active_layout": None
        }
        
        if project_data:
            # Extract project name from folders or file
            folders = project_data.get("folders", [])
            if folders:
                info["folders"] = [folder.get("path", "") for folder in folders]
                # Use first folder name as project name
                first_folder = folders[0].get("path", "")
                if first_folder:
                    info["project_name"] = os.path.basename(first_folder.rstrip("/\\"))
                    info["project_path"] = first_folder
            
            # Check for AutoPlace settings
            settings = project_data.get("settings", {})
            autoplace_settings = settings.get("jorn_auto_place_tabs", {})
            
            if autoplace_settings:
                info["has_autoplace_settings"] = True
                info["autoplace_settings"] = autoplace_settings
                
                # Find available layouts
                layouts_config = autoplace_settings.get("layouts", {})
                info["available_layouts"] = list(layouts_config.keys())

                # Get active layout
                info["active_layout"] = autoplace_settings.get("active_layout")
        
        return info
    
    def create_project_layout_from_template(self, window, template_name, layout_name=None):
        """Create a layout in project settings from a template."""
        from .layout_templates import LayoutTemplates
        
        # Get template
        templates = LayoutTemplates.get_all_templates()
        if template_name not in templates:
            return False, f"Template '{template_name}' not found"
        
        template = templates[template_name]
        
        # Use template name as layout name if not provided
        if not layout_name:
            layout_name = template_name
        
        # Get or create project data
        project_data = window.project_data()
        if not project_data:
            # Create minimal project data
            project_data = {
                "folders": [{"path": "."}],
                "settings": {}
            }
        
        if "settings" not in project_data:
            project_data["settings"] = {}
        
        if "jorn_auto_place_tabs" not in project_data["settings"]:
            project_data["settings"]["jorn_auto_place_tabs"] = {}
        
        autoplace_settings = project_data["settings"]["jorn_auto_place_tabs"]

        # Ensure layouts section exists
        if "layouts" not in autoplace_settings:
            autoplace_settings["layouts"] = {}

        # Add layout to layouts section
        autoplace_settings["layouts"][layout_name] = template.copy()

        # Set as active layout if no active layout exists
        if not autoplace_settings.get("active_layout"):
            autoplace_settings["active_layout"] = layout_name
            autoplace_settings["auto_place_on_activation"] = True
        
        # Save project data
        window.set_project_data(project_data)
        
        # Clear cache
        self.plugin._clear_settings_cache(window.id())
        
        return True, f"Created layout '{layout_name}' from template '{template_name}'"
    
    def export_project_layouts(self, window):
        """Export project layouts to JSON format."""
        project_info = self.get_project_info(window)
        
        if not project_info["has_autoplace_settings"]:
            return None, "No AutoPlace settings found"
        
        # Extract layouts only (check both "layout" and "layout_definition" keys)
        layouts = {}
        for key, value in project_info["autoplace_settings"].items():
            if (key != "_settings" and isinstance(value, dict) and
                ("layout" in value or "layout_definition" in value)):
                layouts[key] = value
        
        if not layouts:
            return None, "No layouts found"
        
        import time
        export_data = {
            "project_name": project_info["project_name"],
            "export_timestamp": time.time(),
            "layouts": layouts,
            "active_layout": project_info["active_layout"]
        }
        
        return export_data, f"Exported {len(layouts)} layouts"
    
    def import_project_layouts(self, window, import_data):
        """Import layouts from exported JSON data."""
        if not isinstance(import_data, dict) or "layouts" not in import_data:
            return False, "Invalid import data format"
        
        layouts = import_data["layouts"]
        if not layouts:
            return False, "No layouts found in import data"
        
        # Validate layouts
        from .layout_definition import LayoutDefinition
        for name, layout_def in layouts.items():
            try:
                LayoutDefinition(name, layout_def)
            except Exception as e:
                return False, f"Invalid layout '{name}': {str(e)}"
        
        # Get or create project data
        project_data = window.project_data()
        if not project_data:
            project_data = {
                "folders": [{"path": "."}],
                "settings": {}
            }
        
        if "settings" not in project_data:
            project_data["settings"] = {}
        
        if "jorn_auto_place_tabs" not in project_data["settings"]:
            project_data["settings"]["jorn_auto_place_tabs"] = {}
        
        autoplace_settings = project_data["settings"]["jorn_auto_place_tabs"]
        
        # Import layouts
        imported_count = 0
        for name, layout_def in layouts.items():
            autoplace_settings[name] = layout_def
            imported_count += 1
        
        # Set active layout if specified and no current active layout
        if "_settings" not in autoplace_settings:
            autoplace_settings["_settings"] = {}
        
        if not autoplace_settings["_settings"].get("active_layout"):
            active_layout = import_data.get("active_layout")
            if active_layout and active_layout in layouts:
                autoplace_settings["_settings"]["active_layout"] = active_layout
                autoplace_settings["_settings"]["auto_place_on_activation"] = True
        
        # Save project data
        window.set_project_data(project_data)
        
        # Clear cache
        self.plugin._clear_settings_cache(window.id())
        
        return True, f"Imported {imported_count} layouts"
    
    def cleanup_project_settings(self, window):
        """Clean up project settings by removing invalid or unused entries."""
        project_data = window.project_data()
        if not project_data or "settings" not in project_data:
            return False, "No project settings found"
        
        autoplace_settings = project_data["settings"].get("jorn_auto_place_tabs", {})
        if not autoplace_settings:
            return False, "No AutoPlace settings found"
        
        # Validate layouts
        from .layout_definition import LayoutDefinition
        valid_layouts = {}
        invalid_layouts = []
        
        for key, value in autoplace_settings.items():
            if key == "_settings":
                continue
            
            # More robust layout detection - check for any layout structure
            if (isinstance(value, dict) and
                ("layout" in value or "layout_definition" in value or "group_rules" in value)):
                try:
                    LayoutDefinition(key, value)
                    valid_layouts[key] = value
                except Exception as e:
                    # Store error details for better debugging
                    invalid_layouts.append(f"{key} (Error: {str(e)})")
        
        # WARNING: This deletes user data! In production, should backup first
        # Remove invalid layouts (extract key name from error string)
        for invalid_entry in invalid_layouts:
            invalid_key = invalid_entry.split(" (Error:")[0]  # Extract key from "key (Error: ...)"
            if invalid_key in autoplace_settings:
                del autoplace_settings[invalid_key]
        
        # Update active layout if it was invalid
        settings_section = autoplace_settings.get("_settings", {})
        active_layout = settings_section.get("active_layout")
        
        if active_layout and active_layout not in valid_layouts:
            if valid_layouts:
                # Set to first valid layout
                settings_section["active_layout"] = list(valid_layouts.keys())[0]
            else:
                # No valid layouts, remove active layout
                settings_section.pop("active_layout", None)
        
        # Save if changes were made
        if invalid_layouts:
            window.set_project_data(project_data)
            self.plugin._clear_settings_cache(window.id())
            return True, f"Removed {len(invalid_layouts)} invalid layouts: {', '.join(invalid_layouts)}"
        
        return True, "No cleanup needed - all layouts are valid"

    def preview_cleanup(self, window):
        """Preview what cleanup would do without making changes."""
        project_data = window.project_data()
        if not project_data or "settings" not in project_data:
            return False, "No project settings found"

        autoplace_settings = project_data["settings"].get("jorn_auto_place_tabs", {})
        if not autoplace_settings:
            return False, "No AutoPlace settings found"

        # Validate layouts and collect error details
        from .layout_definition import LayoutDefinition
        invalid_layouts = []

        for key, value in autoplace_settings.items():
            if key == "_settings":
                continue

            if (isinstance(value, dict) and
                ("layout" in value or "layout_definition" in value or "group_rules" in value)):
                try:
                    LayoutDefinition(key, value)
                except Exception as e:
                    invalid_layouts.append(f"{key}: {str(e)}")

        if not invalid_layouts:
            return True, "No invalid layouts found - no cleanup needed"

        preview_text = f"Found {len(invalid_layouts)} invalid layouts:\n\n"
        for invalid in invalid_layouts:
            preview_text += f"• {invalid}\n"

        preview_text += "\nThese layouts would be DELETED during cleanup."
        preview_text += "\nConsider backing up your project file first."

        return True, preview_text

    def get_project_settings_summary(self, window):
        """Get a summary of project settings for display."""
        project_info = self.get_project_info(window)
        
        summary = {
            "project_name": project_info["project_name"] or "Unnamed Project",
            "has_settings": project_info["has_autoplace_settings"],
            "layout_count": len(project_info["available_layouts"]),
            "active_layout": project_info["active_layout"],
            "folders": project_info["folders"]
        }
        
        if project_info["has_autoplace_settings"]:
            settings = project_info["autoplace_settings"]
            global_settings = settings.get("_settings", {})
            
            summary.update({
                "auto_place_on_activation": global_settings.get("auto_place_on_activation", False),
                "auto_place_on_load": global_settings.get("auto_place_on_load", False),
                "enable_debug_prints": global_settings.get("enable_debug_prints", False)
            })
        
        return summary
