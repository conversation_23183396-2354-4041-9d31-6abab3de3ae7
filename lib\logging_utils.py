"""
Logging Utilities for AutoPlace Tab Plugin

Centralized logging with proper Sublime Text integration.
"""

from __future__ import annotations
from typing import Optional
import sublime


class Logger:
    """Centralized logger for the AutoPlace plugin."""
    
    def __init__(self, plugin_name: str):
        """
        Initialize logger.
        
        Args:
            plugin_name: Name of the plugin for log prefixing
        """
        self.plugin_name = plugin_name
        self._debug_enabled = False
    
    def set_debug_enabled(self, enabled: bool) -> None:
        """
        Enable or disable debug logging.
        
        Args:
            enabled: Whether debug logging should be enabled
        """
        self._debug_enabled = enabled
    
    def debug(self, message: str) -> None:
        """
        Log a debug message.
        
        Args:
            message: The debug message to log
        """
        if self._debug_enabled:
            sublime.log_message(f"[{self.plugin_name}] DEBUG: {message}")
    
    def info(self, message: str) -> None:
        """
        Log an info message.
        
        Args:
            message: The info message to log
        """
        sublime.log_message(f"[{self.plugin_name}] INFO: {message}")
    
    def warning(self, message: str) -> None:
        """
        Log a warning message.
        
        Args:
            message: The warning message to log
        """
        sublime.log_message(f"[{self.plugin_name}] WARNING: {message}")
    
    def error(self, message: str) -> None:
        """
        Log an error message.
        
        Args:
            message: The error message to log
        """
        sublime.log_message(f"[{self.plugin_name}] ERROR: {message}")
    
    def user_error(self, message: str, show_dialog: bool = False) -> None:
        """
        Show error to user via status bar or dialog.
        
        Args:
            message: The error message to show
            show_dialog: Whether to show a dialog instead of status message
        """
        if show_dialog:
            sublime.error_message(f"{self.plugin_name} Error: {message}")
        else:
            sublime.status_message(f"{self.plugin_name} Error: {message}")
        self.error(f"USER ERROR: {message}")
    
    def user_warning(self, message: str) -> None:
        """
        Show warning to user via status bar.
        
        Args:
            message: The warning message to show
        """
        sublime.status_message(f"{self.plugin_name} Warning: {message}")
        self.warning(f"USER WARNING: {message}")
    
    def user_info(self, message: str) -> None:
        """
        Show info message to user via status bar.
        
        Args:
            message: The info message to show
        """
        sublime.status_message(f"{self.plugin_name}: {message}")
        self.info(f"USER INFO: {message}")


# Global logger instance
_logger: Optional[Logger] = None


def get_logger() -> Logger:
    """
    Get the global logger instance.
    
    Returns:
        The global logger instance
    """
    global _logger
    if _logger is None:
        _logger = Logger("AutoPlace")
    return _logger


def init_logger(plugin_name: str) -> Logger:
    """
    Initialize the global logger.
    
    Args:
        plugin_name: Name of the plugin
        
    Returns:
        The initialized logger instance
    """
    global _logger
    _logger = Logger(plugin_name)
    return _logger
