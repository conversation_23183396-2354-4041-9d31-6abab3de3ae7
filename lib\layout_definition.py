"""
Layout Definition Classes for Jorn_AutoPlaceTabs

Provides layout-centric architecture where rules are children of layouts.
"""

import sublime


class LayoutDefinition:
    """Represents a complete layout definition with structure, rules, and settings."""
    
    def __init__(self, name, definition):
        """Initialize layout definition from JSON structure."""
        self.name = name

        # Handle both old metadata format and new direct format
        if "metadata" in definition:
            # Old format with metadata wrapper
            self.metadata = definition["metadata"]
            self.layout = definition["layout"]
            self.rules = definition["rules"]
        else:
            # New direct format
            self.metadata = {
                "name": definition.get("name", name),
                "description": definition.get("description", "")
            }
            self.layout = definition.get("layout_definition", definition.get("layout", {}))
            self.rules = definition.get("group_rules", definition.get("rules", {}))

        self.settings = definition.get("settings", {})
        self.layout_type = definition.get("layout_type", "custom")

        # Validate required fields
        self._validate()
    
    def _validate(self):
        """Validate layout definition structure."""
        required_layout_fields = ["cols", "rows", "cells"]
        for field in required_layout_fields:
            if field not in self.layout:
                raise ValueError(f"Layout '{self.name}' missing required field: {field}")
        
        if not isinstance(self.rules, dict):
            raise ValueError(f"Layout '{self.name}' rules must be a dictionary")
    
    def get_display_name(self):
        """Get human-readable display name."""
        return self.metadata.get("name", self.name)
    
    def get_description(self):
        """Get layout description."""
        return self.metadata.get("description", "")

    @property
    def group_rules(self):
        """Get group rules (alias for rules for backward compatibility)."""
        return self.rules
    
    def get_group_count(self):
        """Get number of groups defined in this layout."""
        return len(self.layout["cells"])
    
    def get_rule_count(self):
        """Get number of rule groups defined."""
        return len(self.rules)
    
    def get_threshold(self, threshold_name, default=None):
        """Get a threshold setting with fallback to default."""
        return self.settings.get(threshold_name, default)
    
    def get_group_rule(self, group_id):
        """Get rule definition for a specific group."""
        rules = self.rules.get(str(group_id))
        if isinstance(rules, list) and rules:
            return rules[0]  # Return first rule for backward compatibility
        elif isinstance(rules, dict):
            return rules  # Legacy single rule format
        return None

    def get_group_rules(self, group_id):
        """Get all rule definitions for a specific group."""
        rules = self.rules.get(str(group_id))
        if isinstance(rules, list):
            return rules
        elif isinstance(rules, dict):
            return [rules]  # Wrap single rule in array
        return []

    def get_group_name(self, group_id):
        """Get display name for a group."""
        rule = self.get_group_rule(group_id)
        if rule:
            # Use description field (new format) or name field (legacy)
            return rule.get("description", rule.get("name", f"Group {group_id}"))
        return f"Group {group_id}"
    
    def get_all_group_names(self):
        """Get all group names as a dictionary."""
        return {
            group_id: self.get_group_name(group_id) 
            for group_id in range(self.get_group_count())
        }
    
    def has_rules_for_group(self, group_id):
        """Check if there are rules defined for a group."""
        return str(group_id) in self.rules
    
    def get_layout_structure(self):
        """Get the Sublime layout structure."""
        return self.layout.copy()
    
    def get_setting(self, setting_name, default=None):
        """Get a layout-specific setting."""
        return self.settings.get(setting_name, default)
    
    def to_dict(self):
        """Convert layout definition back to dictionary format."""
        return {
            "metadata": self.metadata,
            "layout": self.layout,
            "rules": self.rules,
            "settings": self.settings
        }
    
    def __str__(self):
        """String representation for debugging."""
        return f"LayoutDefinition(name='{self.name}', groups={self.get_group_count()}, rules={self.get_rule_count()})"
    
    def __repr__(self):
        return self.__str__()


class LayoutManager:
    """Manages layout definitions and provides layout operations."""
    
    def __init__(self, plugin):
        """Initialize with reference to main plugin."""
        self.plugin = plugin
        self._layout_cache = {}
    
    def get_available_layouts(self, window):
        """Get all available layout definitions for a window."""
        effective_settings = self.plugin._get_effective_settings(window)
        layouts = {}

        # Check for layouts in the settings
        layouts_config = effective_settings.get("layouts", {})
        for layout_name, layout_def in layouts_config.items():
            try:
                layouts[layout_name] = LayoutDefinition(layout_name, layout_def)
            except ValueError as e:
                self.plugin._debug_print(f"Invalid layout '{layout_name}': {e}")

        return layouts
    
    def get_active_layout(self, window):
        """Get the currently active layout definition."""
        effective_settings = self.plugin._get_effective_settings(window)
        active_layout_name = effective_settings.get("active_layout")

        if not active_layout_name:
            self.plugin._debug_print("No active layout configured")
            return None

        layouts_config = effective_settings.get("layouts", {})
        if active_layout_name not in layouts_config:
            self.plugin._debug_print(f"Active layout '{active_layout_name}' not found")
            return None

        try:
            layout_def = LayoutDefinition(active_layout_name, layouts_config[active_layout_name])
            self.plugin._debug_print(f"Active layout: {layout_def}")
            return layout_def
        except ValueError as e:
            self.plugin._debug_print(f"Invalid active layout '{active_layout_name}': {e}")
            return None
    
    def set_active_layout(self, window, layout_name):
        """Set the active layout for a window."""
        effective_settings = self.plugin._get_effective_settings(window)
        layouts_config = effective_settings.get("layouts", {})

        if layout_name not in layouts_config:
            raise ValueError(f"Layout '{layout_name}' not found")
        
        # Update the project settings to set active layout
        project_data = window.project_data()
        if not project_data:
            raise ValueError("No project file is open")

        if "settings" not in project_data:
            project_data["settings"] = {}

        if "jorn_auto_place_tabs" not in project_data["settings"]:
            project_data["settings"]["jorn_auto_place_tabs"] = {}

        project_data["settings"]["jorn_auto_place_tabs"]["active_layout"] = layout_name
        window.set_project_data(project_data)

        # Clear cache to pick up new settings immediately
        self.plugin._clear_settings_cache(window.id())
        self.plugin._debug_print(f"Active layout changed to '{layout_name}', cache cleared")
        
        self.plugin._debug_print(f"Set active layout to: {layout_name}")
    
    def apply_layout(self, window, layout_definition):
        """Apply a complete layout definition to a window."""
        if not isinstance(layout_definition, LayoutDefinition):
            raise ValueError("Expected LayoutDefinition instance")
        
        # Apply the physical layout structure
        try:
            window.set_layout(layout_definition.get_layout_structure())
            self.plugin._debug_print(f"Applied layout structure for: {layout_definition.name}")
        except Exception as e:
            self.plugin._debug_print(f"Failed to apply layout structure: {e}")
            return False
        
        # Place all tabs according to the layout's rules
        self._place_all_tabs_for_layout(window, layout_definition)
        
        return True
    
    def _place_all_tabs_for_layout(self, window, layout_definition):
        """Place all tabs according to a layout's rules with order preservation."""
        placed_count = 0

        # Collect all tabs that need to be moved, preserving their relative order
        tabs_to_move = []
        for view in window.views():
            target_group = self._determine_target_group_for_layout(view, window, layout_definition)
            if target_group is not None:
                current_group, current_index = window.get_view_index(view)
                if current_group != target_group:
                    tabs_to_move.append({
                        'view': view,
                        'target_group': target_group,
                        'original_index': current_index,
                        'original_group': current_group
                    })

        # Sort by original position to maintain relative order
        tabs_to_move.sort(key=lambda x: (x['original_group'], x['original_index']))

        # Move tabs using the plugin's placement logic for proper index calculation
        for tab_info in tabs_to_move:
            try:
                # Use the plugin's placement method which handles proper indexing
                self.plugin._place_tab(tab_info['view'], tab_info['target_group'])
                placed_count += 1
            except Exception as e:
                self.plugin._debug_print(f"Failed to place tab: {e}")

        self.plugin._debug_print(f"Placed {placed_count} tabs for layout: {layout_definition.name}")
    
    def _determine_target_group_for_layout(self, view, window, layout_definition):
        """Determine target group for a view using a specific layout's rules."""
        # Use the centralized rule engine
        return self.plugin.rule_engine._determine_target_group_layout_centric(view, window, layout_definition)
    
    def clear_cache(self):
        """Clear layout cache."""
        self._layout_cache.clear()
