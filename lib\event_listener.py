"""
Event Listener for AutoPlace Tab Plugin

Handles all Sublime Text events for automatic tab placement.
"""

from __future__ import annotations
import os
import time
from typing import Optional
import sublime
import sublime_plugin


class AutoPlaceEventListener(sublime_plugin.EventListener):
    """Event listener for automatic tab placement."""
    
    def __init__(self):
        """Initialize event listener."""
        super().__init__()

    @property
    def plugin(self):
        """Get plugin instance dynamically."""
        # Import here to avoid circular imports
        from ..Jorn_AutoPlaceTabs import Jorn_AutoPlaceTabsCommand
        return Jorn_AutoPlaceTabsCommand.instance()
    
    def on_activated_async(self, view: sublime.View) -> None:
        """Handle tab activation for auto-placement."""
        if not view or not view.window() or not self.plugin:
            return

        # Track view activation for semantic detection
        self.plugin.semantic_detector.track_view_activated(view)

        # Check if this view is already being placed (per-view concurrency protection)
        view_id = view.id()
        if view_id in self.plugin._placement_locks:
            return

        window = view.window()
        if not self.plugin._get_setting("auto_place_on_activation", True, window):
            return

        # Determine target group once
        target_group = self.plugin.rule_engine.determine_target_group(view)
        if target_group is None:
            return

        # Check if already in correct group
        current_group, _ = window.get_view_index(view)
        if current_group == target_group:
            return

        # Check rate limiting
        if not self.plugin._check_placement_frequency(window):
            self.plugin._debug_print("Rate limit exceeded, skipping placement")
            return

        self.plugin._place_tab(view, target_group)

    def on_load_async(self, view: sublime.View) -> None:
        """Handle file load for auto-placement."""
        if not view or not view.window() or not self.plugin:
            return

        # Track view opening for semantic detection
        self.plugin.semantic_detector.track_view_opened(view)

        # Check if this view is already being placed (per-view concurrency protection)
        view_id = view.id()
        if view_id in self.plugin._placement_locks:
            return

        window = view.window()
        if not self.plugin._get_setting("auto_place_on_load", True, window):
            return

        if not self.plugin._check_placement_frequency(window):
            return

        # Small delay to ensure file is fully loaded
        def delayed_place():
            target_group = self.plugin.rule_engine.determine_target_group(view)
            if target_group is not None:
                current_group, _ = view.window().get_view_index(view)
                if current_group != target_group:
                    self.plugin._place_tab(view, target_group)

        sublime.set_timeout_async(delayed_place, 100)

    def on_window_command(self, window: sublime.Window, command_name: str, args: dict) -> None:
        """Handle window commands that might change project settings."""
        if not self.plugin:
            return
            
        if command_name in ["open_project", "close_project", "switch_project"]:
            # Clear settings cache when project changes
            self.plugin._clear_settings_cache(window.id())

    def on_post_window_command(self, window: sublime.Window, command_name: str, args: dict) -> None:
        """Handle post-window commands that might change project settings."""
        if not self.plugin:
            return
            
        if command_name in ["open_project", "close_project", "switch_project"]:
            # Clear settings cache when project changes
            self.plugin._clear_settings_cache(window.id())
        elif command_name == "save_project_as":
            # Clear cache when project is saved with new name
            self.plugin._clear_settings_cache(window.id())

    def on_post_save_async(self, view: sublime.View) -> None:
        """Handle file save events that might affect project settings."""
        if not view or not view.window() or not self.plugin:
            return
        
        window = view.window()
        file_name = view.file_name()
        project_file_name = window.project_file_name()
        
        # Check if the saved file is the project file
        if (file_name and project_file_name and 
            os.path.abspath(file_name) == os.path.abspath(project_file_name)):
            self.plugin._debug_print(f"Project file saved: {file_name}")
            self.plugin._clear_settings_cache(window.id())

    def on_modified_async(self, view: sublime.View) -> None:
        """Handle view modification for semantic tracking."""
        if view and self.plugin:
            self.plugin.semantic_detector.track_view_modified(view)

    def on_pre_close_window(self, window: sublime.Window) -> None:
        """Clean up cache when window is closed."""
        if window and self.plugin:
            self.plugin._clear_settings_cache(window.id())
